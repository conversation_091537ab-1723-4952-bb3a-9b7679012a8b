/// <reference types="vitest" />
/// <reference types="vite/client" />

import reactBabel from '@vitejs/plugin-react'
import reactSwc from '@vitejs/plugin-react-swc'
import dotenv from 'dotenv'
import path from 'path'
import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'

dotenv.config()
const PORT = process?.env?.APP_PORT ? JSON.parse(process?.env?.APP_PORT) : 3000
const IS_OPTIMIZATION = process?.env?.APP_IS_OPTIMIZATION ? JSON.parse(process?.env?.APP_IS_OPTIMIZATION) : true
const LOCAL_DEV = process?.env?.APP_LOCAL_DEV ? JSON.parse(process?.env?.APP_LOCAL_DEV) : false
const TEST_STAND = process?.env?.APP_TEST_STAND ? JSON.parse(process?.env?.APP_TEST_STAND) : false
const URL_STAND = process?.env?.APP_URL_STAND ?? 'http://neptune-dev.ic.ntcees.ln:8080'
const IS_VIEW_DEV_PAGE = process?.env?.APP_VIEW_DEV_PAGE ? JSON.parse(process?.env?.APP_VIEW_DEV_PAGE) : true
const IS_VIEW_WS_LOG = process?.env?.APP_VIEW_WS_LOG ? JSON.parse(process?.env?.APP_VIEW_WS_LOG) : false
const getTargetForProxy = () => {
  if (LOCAL_DEV) {
    return 'http://localhost:8080'
  }
  if (TEST_STAND) {
    return 'http://neptune-test.ic.ntcees.ln:8080/'
  }

  return URL_STAND
}
const WEB_VERSION = process?.env?.npm_package_version ?? ''

export default defineConfig(({ command }) => {
  const projectRoot = path.resolve(__dirname)

  return {
    publicDir: 'build',
    plugins: [
      command === 'build'
        ? reactBabel({
            babel: {
              plugins: ['babel-plugin-react-generate-property'],
            },
          })
        : reactSwc(),
      svgr(),
    ],
    envPrefix: 'APP',
    define: {
      isOptimization: JSON.stringify(IS_OPTIMIZATION),
      isViewDevPage: JSON.stringify(IS_VIEW_DEV_PAGE) ?? true,
      isViewWSLog: JSON.stringify(IS_VIEW_WS_LOG) ?? false,
      webVersion: JSON.stringify(WEB_VERSION),
    },
    optimizeDeps: {
      include: ['@mui/material'],
    },
    resolve: {
      alias: {
        '/@/': path.resolve(projectRoot, 'src'),
        app: path.resolve(projectRoot, 'src/app'),
        entities: path.resolve(projectRoot, 'src/entities'),
        features: path.resolve(projectRoot, 'src/features'),
        pages: path.resolve(projectRoot, 'src/pages'),
        shared: path.resolve(projectRoot, 'src/shared'),
        stores: path.resolve(projectRoot, 'src/stores'),
        widgets: path.resolve(projectRoot, 'src/widgets'),
      },
    },
    server: {
      port: PORT,
      proxy: {
        '/api': {
          target: getTargetForProxy(),
          changeOrigin: false,
          secure: false,
          ws: true,
          headers: {
            Connection: 'upgrade',
            'Access-Control-Allow-Origin': '*',
          },
        },
      },
    },
    build: {
      minify: true,
      rollupOptions: {},
      assetsInlineLimit: 0, // Отключаем лимит для встраивания файлов в JavaScript
      chunkSizeWarningLimit: 2000, // Порог предупреждения о размере чанка,
      outDir: 'build',
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: [path.resolve(projectRoot, 'src/setupTests.ts')],
      include: ['src/**/*.{test,spec}.{ts,tsx}'],
      exclude: [],
      coverage: {
        provider: 'v8',
        reporter: ['text'],
        reportsDirectory: path.resolve(projectRoot, 'coverage'),
        enabled: false,
      },
    },
  }
})
