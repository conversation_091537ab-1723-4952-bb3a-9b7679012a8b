import { useContext } from 'react'

import { CollapsedProviderContext, LOCAL_STORAGE_COLLAPSED_KEY } from './CollapsedProviderContext'

export function useCollapsed() {
  const { collapsed, setCollapsed } = useContext(CollapsedProviderContext)

  const toggleCollapsed = () => {
    if (setCollapsed) {
      setCollapsed(!collapsed)
    }
    localStorage.setItem(LOCAL_STORAGE_COLLAPSED_KEY, JSON.stringify(!collapsed))
  }

  return { collapsed, toggleCollapsed }
}
