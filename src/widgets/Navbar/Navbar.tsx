import { useCollapsed } from 'app/providers/CollapsedProvider'
import { observer } from 'mobx-react'
import { useCallback, useRef, useState } from 'react'
import { useLocation } from 'react-router-dom'
import { classNames } from 'shared/lib/classNames/classNames.ts'
import { Icon } from 'shared/ui/Icon'
import { useStore } from 'stores/useStore.ts'

import { getNavbarTitle } from './lib'
import cls from './Navbar.module.scss'
import { NavbarModal } from './ui'

export const Navbar = observer(() => {
  const { collapsed } = useCollapsed()
  const location = useLocation()
  const { pathname } = location
  const { authStore, godModeStore } = useStore()
  const { userDetail } = authStore
  const { godMode } = godModeStore
  const { initials } = userDetail
  const [isOpenDetailModal, setIsOpenDetailModal] = useState(false)
  const buttonRef = useRef<HTMLButtonElement | null>(null)

  const handleAvatarClick = useCallback(() => setIsOpenDetailModal((prev) => !prev), [])
  const handleClose = useCallback(() => setIsOpenDetailModal(false), [])

  return (
    <div className={classNames(cls.navbar, { 'god-mode_bg-color': !!godMode }, [])}>
      <h2 className={classNames(cls.title, { [cls.collapsed]: collapsed }, [])}>{getNavbarTitle(pathname)}</h2>
      <div className={cls.avatarContainer}>
        {isOpenDetailModal && <NavbarModal onClose={handleClose} buttonRef={buttonRef} />}
        <button type='button' ref={buttonRef} className={cls.avatarBox} onClick={handleAvatarClick}>
          <Icon name='avatarDefault' width={18} />
          <div className={cls.avatarName}>{initials}</div>
        </button>
      </div>
    </div>
  )
})
