interface ItemProps {
  label: 'Светлая' | 'Темная' | 'Серая' | 'Зеленая' | 'Коричневая'
  value: 'light' | 'dark' | 'gray' | 'green' | 'brown'
  icon: 'color'
}

export const navbarItems: ItemProps[] = [
  {
    label: 'Светлая',
    value: 'light',
    icon: 'color',
  },
  {
    label: 'Темная',
    value: 'dark',
    icon: 'color',
  },
  {
    label: 'Серая',
    value: 'gray',
    icon: 'color',
  },
  {
    label: 'Зеленая',
    value: 'green',
    icon: 'color',
  },
  {
    label: 'Коричневая',
    value: 'brown',
    icon: 'color',
  },
]
