.navbar {
  width: 100%;
  height: var(--navbar-height);
  background: var(--primary-color);
  display: flex;
  align-items: center;
  padding: 20px;
  position: relative;
  transition: background-color 0.2s ease;
}

.title {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color-invert);
  margin-left: 210px;
  transition: margin-left 0.3s;
}

.collapsed {
  margin-left: 40px;
}

.avatarContainer {
  margin-left: auto;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  position: relative;
  cursor: pointer;
}

.avatarName {
  white-space: nowrap;
  color: var(--primary-color-invert);
  margin-left: 10px;
  user-select: none;
}

.avatarBox {
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  cursor: pointer;
}
