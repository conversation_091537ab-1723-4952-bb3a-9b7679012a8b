.modalDetail {
  width: 320px;
  min-height: 160px;
  position: absolute;
  top: 40px;
  right: -10px;
  z-index: 15;
  background-color: var(--background-color-secondary);
  box-shadow: 0 5px 18px 12px rgb(34 60 80 / 20%);
  border-radius: 20px;
  padding: 8px;
  cursor: default;
}

.modalDetailInfoContainer {
  display: flex;
  height: 40%;
  width: 100%;
  padding: 0 12px;
}

.modalDetailInfoContainerLeft {
  width: 24%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalDetailInfoContainerRight {
  width: 76%;
  padding: 0 10px;
}

.modalDetailTitle {
  color: var(--text-color);
  height: 50%;
  display: flex;
  align-items: flex-end;
  user-select: none;
  font-size: 14px;
}

.modalDetailDepartment {
  color: gray;
  height: 50%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 14px;
  user-select: none;
}

.modalDetailRoles {
  display: flex;
  flex-wrap: wrap;
  padding: 0 12px;
}

.modalDetailExit {
  padding: 0 12px;
  margin-top: 6px;
}

.modalDetailRole {
  color: var(--text-gray);
  border: solid 1px var(--text-gray);
  width: auto;
  border-radius: 10px;
  padding: 0 8px;
  margin: 2px;
  user-select: none;
  font-size: 14px;
}

.dropDownContainer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-top: 6px;
}

.buttonDropDown {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 !important;
}

.buttonDropDownContent {
  width: 100%;
  display: flex !important;
  flex-direction: row;
  align-items: center !important;
  justify-content: flex-start !important;
  padding: 0 !important;
  color: var(--text-color) !important;
}

.combobox > div {
  height: 20px;
  padding: 0 10px;

  &::after {
    content: none;
  }

  &::before {
    content: none;
  }
}

.iconLogout {
  margin-right: 8px;
}

.godModeSwitch {
  padding: 0 24px;
  margin-top: 8px;
}

.godModeIcon {
  color: var(--primary-color);
}
