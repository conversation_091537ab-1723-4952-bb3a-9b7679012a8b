import { Theme, useTheme } from 'app/providers/ThemeProvider'
import { observer } from 'mobx-react'
import { FC, MutableRefObject, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { disconnectWebSocket } from 'shared/lib/axios/axios.ts'
import { useOnClickOutside } from 'shared/lib/useOnClickOutside'
import { Icon } from 'shared/ui'
import { Button } from 'shared/ui/Button'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { useStore } from 'stores/useStore.ts'
import { navbarItems } from 'widgets/Navbar/lib/navbarItems.ts'

import cls from './NavbarModal.module.scss'

interface NavbarModalProps {
  onClose: () => void
  buttonRef: MutableRefObject<HTMLButtonElement | null>
}

export const NavbarModal: FC<NavbarModalProps> = observer((props) => {
  const { onClose, buttonRef } = props
  const { toggleTheme } = useTheme()
  const { authStore, godModeStore } = useStore()
  const { userDetail, logout } = authStore
  const { toggleGodMode, godMode } = godModeStore
  const { name, roles, department } = userDetail
  const modalContainerRef = useRef<HTMLDivElement>(null)
  const selectThemeRef = useRef<HTMLElement | null>(null)
  const history = useNavigate()
  const initTheme = localStorage.getItem('theme') ?? 'light'
  const [themeSelect, setThemeSelect] = useState(initTheme)

  useOnClickOutside(modalContainerRef, () => {
    onClose()
  }, [buttonRef, selectThemeRef])

  const handleOpen = () => {
    setTimeout(() => {
      const menuContainer = document.querySelector('#menu-')
      if (menuContainer) {
        selectThemeRef.current = menuContainer as HTMLElement
      }
    }, 0)
  }

  const handleLogout = () => {
    logout().then(async () => {
      await disconnectWebSocket()
      history('/login')
    })
  }

  return (
    <div ref={modalContainerRef} className={cls.modalDetail}>
      <div className={cls.modalDetailInfoContainer}>
        <div className={cls.modalDetailInfoContainerLeft}>
          <Icon name='avatarDefault' width={60} />
        </div>
        <div className={cls.modalDetailInfoContainerRight}>
          <div className={cls.modalDetailTitle}>{name}</div>
          <div className={cls.modalDetailDepartment}>{department}</div>
        </div>
      </div>
      <div className={cls.modalDetailRoles}>
        {roles?.map(({ role, description }: { description: string; role: string; mandatory: boolean }) => {
          return (
            <div className={cls.modalDetailRole} key={`role-${role}`}>
              {description}
            </div>
          )
        })}
      </div>
      <div className={cls.dropDownContainer}>
        <Select
          id='test'
          className={cls.combobox}
          items={navbarItems}
          value={themeSelect}
          variant='standard'
          onOpen={handleOpen}
          onChange={(value) => {
            setThemeSelect(value)
            toggleTheme(value as Theme)
          }}
        />
      </div>
      <div className={cls.godModeSwitch}>
        <Switch checked={!!godMode} onChange={toggleGodMode} label='Режим Бога' />
      </div>
      <div className={cls.modalDetailExit}>
        <Button className={cls.buttonDropDown} variant='text' onClick={handleLogout}>
          <div className={cls.buttonDropDownContent}>
            <div className={cls.iconLogout}>
              <Icon name='exit' width={20} height={20} />
            </div>
            <div>Выйти</div>
          </div>
        </Button>
      </div>
    </div>
  )
})
