import Tab from '@mui/material/Tab'
import TabsMui from '@mui/material/Tabs'
import { type ROLES } from 'entities/shared/roles.entities.ts'
import { classNames } from 'shared/lib/classNames/classNames'
import { Icon } from 'shared/ui/Icon'
import { type IconNameProps } from 'shared/ui/Icon/Icon.type'
import { useStore } from 'stores/useStore'

import cls from './Tabs.module.scss'

export interface ItemsProps {
  key: string
  label: string
  icon?: IconNameProps
  rules?: ROLES[]
  disabled?: boolean
  isView: boolean
}
interface TabsProps {
  className?: string
  onChange?: (value: string) => void
  selectedValue?: string
  items: ItemsProps[]
}

export const Tabs = (props: TabsProps) => {
  const { className, onChange, selectedValue, items } = props

  const { authStore } = useStore()
  const { userDetail } = authStore
  const roles = userDetail?.roles ?? []
  const isAccess = (rules: ROLES[]) =>
    roles
      .map((el) => el.role)
      .some((el: string) => {
        return rules.some((rule) => rule === el)
      })

  return (
    <div className={classNames(cls.Tabs, {}, className ? [className] : [])}>
      <TabsMui
        value={selectedValue}
        onChange={(_, value: string) => {
          const isEdit = JSON.parse(localStorage.getItem('editMode') as string) ?? false
          if (isEdit) {
            const answer = window.confirm('У вас есть несохраненные изменения . Вы действительно хотите перейти ?')
            if (answer) {
              onChange && onChange(value)
            }
          } else {
            onChange && onChange(value)
          }
        }}
        // aria-label="basic tabs example"
      >
        {items.map(({ key, label, rules, icon, disabled }) => {
          if (!rules?.length || isAccess(rules)) {
            return (
              <Tab
                key={`tab-${key}`}
                value={key}
                label={
                  <div className={classNames(cls.Label, {}, [])}>
                    {icon && (
                      <div className={classNames(cls.LabelIcon, {}, [])}>
                        <Icon width={18} name={icon} />
                      </div>
                    )}
                    {label}
                  </div>
                }
                disabled={disabled}
              />
            )
          } else return null
        })}
      </TabsMui>
    </div>
  )
}
