// @ts-nocheck
import {
  CustomTreeData,
  CustomTreeDataProps,
  EditingState,
  EditingStateProps,
  FilteringState,
  IntegratedFiltering,
  IntegratedSelection,
  IntegratedSorting,
  IntegratedSummary,
  SelectionState,
  SelectionStateProps,
  SortingDirection,
  SortingState,
  SortingStateProps,
  SummaryState,
  SummaryType,
  TableColumnResizingProps,
  TreeDataState,
} from '@devexpress/dx-react-grid'
import {
  DragDropProvider,
  Grid,
  type Table,
  TableBandHeader,
  TableColumnReordering,
  TableColumnResizing,
  TableColumnVisibility,
  TableFilterRow,
  TableFixedColumns,
  TableHeaderRow,
  TableInlineCellEditing,
  TableSelection,
  TableSummaryRow,
  TableTreeColumn,
  VirtualTable,
} from '@devexpress/dx-react-grid-material-ui'
import Paper from '@mui/material/Paper'
import { ITableFilters } from 'entities/widgets/Table.entities.ts'
import {
  CSSProperties,
  type Dispatch,
  type ReactNode,
  type SetStateAction,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { IRow, prepareFlatData } from 'shared/lib/prepareData'
import { useDebouncedFunction } from 'shared/lib/useDebouncedFunction'
import { Loader } from 'shared/ui/Loader'
import { type NumberOption } from 'shared/ui/TextField/model/types'

import cls from './Table.module.scss'
import {
  Cell,
  CheckboxComponent,
  EditCell,
  ExpandButtonComponent,
  Row,
  TableBandCell,
  TableHeaderContent,
  TableTreeCell,
  TotalRowComponent,
} from './TableCell'

const MIN_WIDTH = 300
const MIN_HEIGHT = 300

export interface ISorting {
  columnName: string
  direction: SortingDirection
}

const getChildRows: CustomTreeDataProps['getChildRows'] = (row, rootRows) => (row ? row.children : rootRows)

export type TAny = string & boolean & number & Record<string & boolean & number, string & boolean & number>
export type TValue<T> = T

const getRowId = (row: { tabId: number }) => row.tabId
export interface IColumn {
  name: string
  title: string
  width: number | string
  fixed?: 'leftColumns' | 'rightColumns'
  filtering?: {
    filter: string[]
    onFilter: (values: string[]) => void
  }
  selectDataFromRow?: string
  headRender?: (value?: TValue<TAny>, row?: TValue<TAny>) => ReactNode
  render?: (value: TValue<TAny>, row: TValue<TAny>) => ReactNode
  tooltip?: (value: TValue<TAny>, row: TValue<TAny>) => string | ReactNode
  isBlockedSorting?: boolean
  defaultSorting?: boolean
  customSorting?: IntegratedSorting.ColumnExtension['compare']
  editingEnabled?: ((row: TValue<TAny>) => boolean) | boolean
  tempEdits?: string[]
  customSearching?: IntegratedFiltering.ColumnExtension['predicate']
  editType?: 'text' | 'date' | 'groups' | 'number' | 'positiveNumber' | string
  editableMaxLength?: number
  minNumber?: number
  maxNumber?: number
  positiveNumber?: boolean
  isInteger?: boolean
  numberOption?: NumberOption
  canClearCell?: boolean
  isHeadLabelCenter?: boolean
  isHeadLabelRight?: boolean
  isOverFlowVisible?: boolean
  column?: { title: string }
  getCellClassName?: (value: unknown) => string
  getCellRowSpan?: (value: unknown) => number
  /** метод для получения простого значения ячейки, для функции contextMenuItem */
  getCellRenderValue?: (value: TValue<TAny>) => string | undefined
}

export interface IRow extends IColumn {
  startDate: string
  type: string
  disabledEditColumns: string[]
  cellsErrors: string[]
  stylesCell: Record<string, CSSProperties>
}

interface IItemsSort {
  columnName: string
  sortingEnabled: boolean
}
interface IColumnExtensions {
  columnName: string
  width: number | string
}

export interface TableProps {
  tableKey?: string
  rows: any[]
  setRows?: Dispatch<SetStateAction<any | undefined>>
  columns: any[] //IColumn
  className?: string
  height?: number | null
  headerComponents?: ReactNode
  ROW_HEIGHT?: number
  footerComponents?: ReactNode
  divisionBySum?: number
  isSearch?: boolean
  editMode?: boolean
  loading?: boolean
  selectMode?: null | 'many'
  childKey?: string
  selection?: string[]
  setSelection?: (ids: string[]) => void
  expandedRowIds?: any
  setExpandenRowIds?: any
  editingCells?: any
  setEditingCells?: any
  showSelectAll?: boolean
  showSortingControls?: boolean
  showSearchControls?: boolean
  columnSearchDisabled?: any[]
  totalSummaryItems?: any[]
  // hiddenColumns?: any[] //old
  columnBands?: any[]
  isOverFlowVisible?: boolean
  initSearchMode?: boolean
  isForceSearch?: boolean
  headStyle?: any
  hiddenColumnNames?: any
  setHiddenColumnNames?: any
  accepted?: boolean
  sumFixed?: number
  initialData?: any
  tableType?: string
  isJournals?: boolean
  getFilters?: (filters: ITableFilters[]) => void
  disabledDragAndDrop?: boolean
  onColumnWidthsChange?: TableColumnResizingProps['onColumnWidthsChange']
  prepareRowBeforeExtractSummaryValue?: (rows: any) => any
}

const editData = (data: any[], changed: any) => {
  return data.map((row) => {
    const children: any = row?.children?.length > 0 ? editData(row?.children, changed) : []

    const tempEdits =
      changed[row.tabId] && Object.values(changed[row.tabId])[0] !== row[Object.keys(changed[row.tabId])[0]]
        ? row?.tempEdits?.length > 0
          ? Array.from(new Set([...row.tempEdits, Object.keys(changed[row.tabId])[0]]))
          : [Object.keys(changed[row.tabId])[0]]
        : []

    return changed[row.tabId]
      ? {
          ...row,
          ...changed[row.tabId],
          isEdit: tempEdits.length > 0,
          tempEdits,
          children,
          cellsErrors: row.cellsErrors?.filter((item: string) => Object.keys(changed).includes(item)) ?? [],
        }
      : { ...row, children }
  })
}

const calculator = (
  type: SummaryType,
  rows: Array<any>,
  getValue: (row: any) => any,
  prepareRowBeforeExtractValue?: (rows: any) => any,
) => {
  const newRows = prepareRowBeforeExtractValue ? prepareRowBeforeExtractValue(rows) : rows
  if (type === 'name') {
    return IntegratedSummary.defaultCalculator(type, newRows, getValue)
  } else if (type === 'avgExcludeNull') {
    let sum = 0
    let counter = 0
    newRows.forEach((row) => {
      const value = getValue(row)
      if (value !== null) {
        sum += value
        counter++
      }
    })

    return sum / counter
  }

  return IntegratedSummary.defaultCalculator(type, newRows, getValue)
}

export const TableComponent = (props: TableProps) => {
  const {
    initialData,
    rows,
    setRows,
    columns,
    className,
    headerComponents = null,
    height = 500,
    selectMode = null,
    selection,
    setSelection,
    childKey = '',
    editMode = false,
    expandedRowIds = null,
    setExpandenRowIds = null,
    tableKey,
    showSelectAll = true,
    showSortingControls = true,
    showSearchControls = true,
    columnSearchDisabled = [],
    columnBands = [],
    isOverFlowVisible = false,
    initSearchMode = false,
    isForceSearch = false,
    totalSummaryItems = [],
    editingCells = null,
    setEditingCells = null,
    headStyle = {},
    hiddenColumnNames = null,
    setHiddenColumnNames = () => {},
    accepted,
    sumFixed,
    tableType,
    ROW_HEIGHT = 22,
    divisionBySum = null,
    isJournals,
    getFilters,
    loading = false,
    disabledDragAndDrop,
    onColumnWidthsChange,
    prepareRowBeforeExtractSummaryValue,
  } = props

  const rootRef = useRef(null)

  const [width, setWidth] = useState<null | string>(null)

  const debouncedValueLogging = useDebouncedFunction(setWidth, 50)
  const observer = useRef(
    new ResizeObserver((entries) => {
      const { width } = entries[0].contentRect

      debouncedValueLogging(String(width))
    }),
  )

  useEffect(() => {
    if (rootRef.current) {
      observer.current.observe(rootRef.current)
    }
  }, [rootRef, observer])

  const sortingStateColumnExtensions = useMemo(() => {
    const arr: IItemsSort[] = []

    for (const column of columns) {
      if (column.isBlockedSorting) {
        arr.push({ columnName: column.name, sortingEnabled: false })
      }
    }

    return arr
  }, [])

  const widthColumnExtensions = useMemo(() => {
    const arr: IColumnExtensions[] = []

    for (const column of columns) {
      if (column.width) {
        arr.push({ columnName: column.name, width: column.width })
      }
    }

    return arr
  }, [])

  const fixedColumns = useMemo(() => {
    if (columns.length > 0) {
      const isOneLeftColumns = columns.some((el) => el.fixed === 'leftColumns')
      const temp: {
        leftColumns: (symbol | string)[]
        rightColumns: string[]
      } = {
        leftColumns: selectMode && isOneLeftColumns ? [TableSelection.COLUMN_TYPE] : [],
        rightColumns: [],
      }

      for (const column of columns) {
        if (column.fixed) temp[column.fixed].push(column.name)
      }

      return temp
    } else {
      return {
        leftColumns: [],
        rightColumns: [],
      }
    }
  }, [columns])

  const [sorting, setSorting] = useState<ISorting[]>([]) // { columnName: 'city', direction: 'asc' }

  const defaultSorting: SortingStateProps['defaultSorting'] = useMemo(() => {
    const arr: { columnName: string; direction: SortingDirection }[] = []

    for (const column of columns) {
      if (column.defaultSorting) arr.push({ columnName: column.name, direction: 'asc' })
    }
    setSorting(arr)

    return arr
  }, [])

  const integratedSortingColumnExtensions: IntegratedSorting.ColumnExtension[] = useMemo(() => {
    const arr: {
      columnName: string
      compare: IColumn['customSorting']
    }[] = []

    for (const column of columns) {
      if (column.customSorting) {
        arr.push({
          columnName: column.name,
          compare: column.customSorting,
        })
      }
    }

    return arr
  }, [])

  const filteringColumnExtensions: IntegratedFiltering.ColumnExtension[] = useMemo(() => {
    const arr = []

    for (const column of columns) {
      if (column.customSearching) {
        arr.push({
          columnName: column.name,
          predicate: column.customSearching,
        })
      }
    }

    return arr
  }, [])

  const defaultColumnsresize = useMemo(() => columns.map((el) => ({ columnName: el.name, width: el.width })), [])

  const columnsResize = useMemo(() => {
    if (!onColumnWidthsChange) return

    return columns.map((el) => ({ columnName: el.name, width: el.width }))
  }, [columns, onColumnWidthsChange])

  const commitChanges: EditingStateProps['onCommitChanges'] = ({ changed }) => {
    let changedRows
    if (changed) {
      changedRows = editData(rows, changed)
    }
    if (setRows) {
      setRows(changedRows)
    }
  }
  const startEditAction = 'click'
  const selectTextOnEditStart = false

  const [expandedRowIdsLocal, setExpandedRowIdsLocal] = useState<(string | number)[]>([])

  const isTree = rows?.some((el) => el?.children)
  const [expandedAll, setExpandedAll] = useState(true)
  const flatRows: IRow<any>[] = isTree ? prepareFlatData(rows) : []

  useEffect(() => {
    if (expandedRowIds) {
      if (expandedRowIds?.length === 0) {
        setExpandedAll(() => {
          return false
        })
      } else {
        setExpandedAll(() => {
          return (
            flatRows?.filter((item) => (tableType !== 'nsi' ? item.generators : item)).length === expandedRowIds.length
          )
        })
      }
    } else {
      if (expandedRowIdsLocal.length === 0) {
        setExpandedAll(() => {
          return false
        })
      } else {
        setExpandedAll(() => {
          return (
            flatRows?.filter((item) => (tableType !== 'nsi' ? item.generators : item)).length ===
            expandedRowIdsLocal?.length
          )
        })
      }
    }
  }, [expandedRowIdsLocal, expandedRowIds, flatRows])

  const [isSearchMode, setIsSearchMode] = useState(initSearchMode)
  const [filters, setFilters] = useState<ITableFilters[]>([]) // [{ columnName: 'car', value: 'cruze' }]

  useEffect(() => {
    if (!isSearchMode) {
      setFilters([])
    }
  }, [isSearchMode])

  useEffect(() => {
    if (!isJournals) return
    getFilters && getFilters(filters)
  }, [filters])

  const onOpenAll = () => {
    const tempRows = flatRows?.filter((item) => (tableType !== 'nsi' ? item.generators : item)).map((el) => el.tabId)

    if (expandedRowIds) {
      setExpandenRowIds(tempRows)
    } else {
      setExpandedRowIdsLocal(tempRows)
    }
    tableKey && localStorage.setItem(tableKey, JSON.stringify(tempRows))
  }
  const onCloseAll = () => {
    if (expandedRowIds) {
      setExpandenRowIds([])
    } else {
      setExpandedRowIdsLocal([])
    }
    tableKey && localStorage.removeItem(tableKey)
  }

  useEffect(() => {
    if (filters.length > 0) {
      // maybe
      const isWord = filters.some((el) => el.value.trim()?.length > 0)
      if (isWord) {
        if (!expandedAll) {
          onOpenAll()
        }
      } else {
        onCloseAll()
      }
    } // maybe
  }, [filters])

  const filteringStateColumnExtensions = columnSearchDisabled.map((el: any) => ({
    columnName: el,
    filteringEnabled: false,
  }))

  const defaultOrder = useMemo(() => columns.map((el) => el.name), [])

  const order = useMemo(() => {
    if (!disabledDragAndDrop) return

    return columns.map((el) => el.name)
  }, [columns, disabledDragAndDrop])

  const getHeight = () => {
    let res = height
    if (res) {
      if (headerComponents) {
        res = res - 50
      }

      return res
    } else {
      return MIN_HEIGHT
    }
  }
  const [editingCellsLocal, setEditingCellsLocal] = useState([])
  const handleSelection: SelectionStateProps['onSelectionChange'] = (selectArr) => {
    const findRow = (rows: any[], item: string | number): boolean => {
      return rows.some((row: any) => {
        const hasSelected = row.tabId === item && !row.disabledChecked

        return hasSelected || (row?.children?.length && findRow(row?.children, item))
      })
    }
    const newArr = selectArr.filter((item) => findRow(rows, item))
    setSelection && setSelection(newArr as string[])
  }

  const [rowsFinal, setRowsFinal] = useState([])

  useEffect(() => {
    setRowsFinal(rows)
  }, [rows])

  return (
    <div
      id='table-container'
      ref={rootRef}
      className={classNames(cls.Table, { [cls.isOverFlowVisible]: isOverFlowVisible }, [className ? className : ''])}
    >
      {columns.length === 0 || loading ? (
        <div
          className={cls.LoaderContainer}
          style={{
            height: getHeight() !== null ? getHeight() : MIN_HEIGHT,
          }}
        >
          <Loader />
        </div>
      ) : (
        <>
          {headerComponents && (
            <div style={headStyle} className={classNames(cls.Head, {}, [])}>
              {headerComponents}
            </div>
          )}
          <div
            style={{
              height: getHeight() !== null ? getHeight() : MIN_HEIGHT,
            }}
            className={classNames(cls.Body, {}, [])}
          >
            {columns.length > 0 ? (
              <Paper
                style={{
                  width: width ? Number(width) : MIN_WIDTH,
                }}
                className={classNames(cls.Paper, {}, [])}
              >
                <Grid rows={rowsFinal} columns={columns} getRowId={getRowId}>
                  {!disabledDragAndDrop && <DragDropProvider />}
                  <EditingState
                    onCommitChanges={commitChanges}
                    editingCells={editingCells || editingCellsLocal}
                    onEditingCellsChange={setEditingCells || setEditingCellsLocal}
                  />
                  {selectMode && <SelectionState selection={selection} onSelectionChange={handleSelection} />}
                  {selectMode && <IntegratedSelection />}
                  <TreeDataState
                    expandedRowIds={expandedRowIds || expandedRowIdsLocal}
                    onExpandedRowIdsChange={(newValue) => {
                      if (setExpandenRowIds) {
                        setExpandenRowIds(newValue)
                      } else {
                        setExpandedRowIdsLocal(newValue)
                      }
                      if (tableKey) {
                        localStorage.setItem(tableKey, JSON.stringify(newValue))
                      }
                    }}
                  />
                  <CustomTreeData getChildRows={getChildRows} />
                  <FilteringState
                    defaultFilters={[]}
                    filters={filters}
                    onFiltersChange={setFilters}
                    columnExtensions={filteringStateColumnExtensions}
                  />
                  <IntegratedFiltering columnExtensions={filteringColumnExtensions} />
                  <SortingState
                    defaultSorting={defaultSorting}
                    columnExtensions={sortingStateColumnExtensions}
                    sorting={sorting}
                    onSortingChange={setSorting}
                  />
                  <IntegratedSorting columnExtensions={integratedSortingColumnExtensions} />
                  <SummaryState
                    totalItems={totalSummaryItems}
                    // groupItems={groupSummaryItems}
                  />
                  <IntegratedSummary
                    calculator={(type, rows, getValue) =>
                      calculator(type, rows, getValue, prepareRowBeforeExtractSummaryValue)
                    }
                  />
                  <VirtualTable
                    height={getHeight()}
                    estimatedRowHeight={ROW_HEIGHT}
                    cellComponent={(props: Table.DataCellProps) => (
                      <Cell editMode={editMode} accepted={accepted} {...props} />
                    )}
                    rowComponent={(props: Table.DataRowProps) => <Row {...props} rowHeight={ROW_HEIGHT} />}
                    messages={{ noData: 'Нет данных' }}
                    columnExtensions={widthColumnExtensions}
                  />
                  <TableColumnResizing
                    defaultColumnWidths={defaultColumnsresize}
                    columnWidths={columnsResize}
                    onColumnWidthsChange={onColumnWidthsChange}
                  />
                  <TableColumnReordering defaultOrder={defaultOrder} order={order} />
                  {hiddenColumnNames && (
                    <TableColumnVisibility
                      hiddenColumnNames={hiddenColumnNames}
                      onHiddenColumnNamesChange={setHiddenColumnNames}
                    />
                  )}
                  <TableHeaderRow
                    showSortingControls
                    messages={{
                      sortingHint: 'Отсортировать',
                    }}
                    contentComponent={(props) => (
                      <TableHeaderContent
                        {...props}
                        childKey={childKey}
                        expandedAll={expandedAll}
                        setIsSearchMode={setIsSearchMode}
                        onOpenAll={() => {
                          onOpenAll()
                        }}
                        onCloseAll={() => {
                          onCloseAll()
                        }}
                        showSortingControls={showSortingControls}
                        showSearchControls={showSearchControls}
                        sorting={sorting}
                        isForceSearch={isForceSearch}
                        columnSearchDisabled={columnSearchDisabled}
                        onSortingChange={(e: ISorting[]) => {
                          setSorting(e)
                        }}
                      />
                    )}
                  />
                  {selectMode && (
                    <TableSelection
                      showSelectAll={showSelectAll && rows.some((item) => !item.disabledChecked)}
                      cellComponent={(props) => <CheckboxComponent {...props} />}
                    />
                  )}
                  <TableTreeColumn
                    for={childKey}
                    cellComponent={(props) => <TableTreeCell {...props} childKey={childKey} isTree={isTree} />}
                    expandButtonComponent={(props) => <ExpandButtonComponent {...props} />}
                  />
                  <TableSummaryRow
                    itemComponent={(props) => (
                      <TotalRowComponent {...props} sumFixed={sumFixed} divisionBySum={divisionBySum} />
                    )}
                  />
                  {isSearchMode && (
                    <TableFilterRow
                      messages={{
                        filterPlaceholder: 'Поиск',
                      }}
                    />
                  )}
                  {columnBands.length > 0 && (
                    <TableBandHeader
                      columnBands={columnBands}
                      cellComponent={(props) => <TableBandCell {...props} columnBands={columnBands} />}
                    />
                  )}
                  <TableFixedColumns leftColumns={fixedColumns.leftColumns} rightColumns={fixedColumns.rightColumns} />
                  <TableInlineCellEditing
                    startEditAction={startEditAction}
                    selectTextOnEditStart={selectTextOnEditStart}
                    cellComponent={(props) => (
                      <EditCell
                        {...props}
                        editMode={editMode}
                        initialData={initialData}
                        rows={rows}
                        setRows={setRows}
                        tableType={tableType}
                      />
                    )}
                  />
                </Grid>
              </Paper>
            ) : (
              <div className={cls.Empty}>Нет данных</div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
