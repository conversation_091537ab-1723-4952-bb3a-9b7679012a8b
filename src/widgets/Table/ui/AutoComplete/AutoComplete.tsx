import CheckIcon from '@mui/icons-material/Check'
import CloseIcon from '@mui/icons-material/Close'
import React, { Dispatch, FC, SetStateAction, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { prepareFlatData } from 'shared/lib/prepareData'
import { useOnClickOutside } from 'shared/lib/useOnClickOutside'

import cls from './AutoComplete.module.scss'

interface ItemsProps {
  value?: string
  label?: string
  disabled?: boolean
  children: ItemsProps[]
}

interface AutoCompleteProps<T> {
  items?: ItemsProps[]
  placeholder?: string
  isAutoFocus?: boolean
  isGroups?: boolean
  values: T[] // Типизация для массива значений
  setValues: Dispatch<SetStateAction<T[]>> // Типизация для функции установки значений
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void
}

export const AutoComplete: FC<AutoCompleteProps<string | number | undefined>> = (props) => {
  const { items = [], placeholder = '', isAutoFocus = true, values, setValues, onKeyDown, isGroups = false } = props
  const [search, setSearch] = useState('')

  const filterItems = isGroups
    ? items
      ? items
          .map((group: ItemsProps) => {
            return {
              ...group,
              children: group.children.filter((el) => {
                const label = el?.label ? el?.label.toUpperCase() : ''
                const searchName = search.toUpperCase()

                return label.includes(searchName)
              }),
            }
          })
          .filter((el) => el.children.length > 0)
      : []
    : items
      ? items.filter((el) => {
          const label = el?.label ? el?.label.toUpperCase() : ''
          const searchName = search.toUpperCase()

          return label.includes(searchName)
        })
      : []

  const flatItemsForGroup = Array.isArray(items) && isGroups ? prepareFlatData(items) : []

  const [isFocus, setIsFocus] = useState(isAutoFocus)

  const rootRef = useRef(null)
  const inputRef = useRef<HTMLInputElement>(null)

  useOnClickOutside(rootRef, () => setIsFocus(false))

  return (
    <div ref={rootRef} className={classNames(cls.Root, {}, [])}>
      <div>
        <div className={classNames(cls.Label, {}, [])}>{placeholder}</div>
        <div
          className={classNames(cls.InputWrapper, {}, [])} //focused ? 'focused' : ''
          onFocus={() => setIsFocus(true)}
        >
          {isGroups ? (
            <>
              {values.map((item, index: number) => {
                const findName = flatItemsForGroup?.find((el) => el.value === item)?.label ?? ''

                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index}`}>
                    {findName}
                    <div
                      className={classNames(cls.IconContainer, {}, [])}
                      onClick={() => {
                        setValues((prev) => {
                          return prev.filter((el) => el !== item)
                        })
                      }}
                    >
                      <CloseIcon
                        className={cls.CloseIcon}
                        fontSize='small'
                        sx={{
                          color: 'var(--primary-color)',
                        }}
                      />
                    </div>
                  </div>
                )
              })}
            </>
          ) : (
            <>
              {values.map((item, index: number) => {
                const findName = items?.find((el) => el.value === item)?.label ?? ''

                return (
                  <div className={classNames(cls.ChooseContainer, {}, [])} key={`autocomplete-tag-${index}`}>
                    {findName}
                    <div
                      className={classNames(cls.IconContainer, {}, [])}
                      onClick={() => {
                        setValues((prev) => {
                          return prev.filter((el) => el !== item)
                        })
                      }}
                    >
                      <CloseIcon
                        className={cls.CloseIcon}
                        fontSize='small'
                        sx={{
                          color: 'var(--primary-color)',
                        }}
                      />
                    </div>
                  </div>
                )
              })}
            </>
          )}
          <input
            ref={inputRef}
            autoFocus={isFocus}
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            onKeyDown={(e) => {
              if (onKeyDown) {
                onKeyDown(e)
              }
            }}
          />
        </div>
      </div>
      {isFocus && filterItems?.length > 0 ? (
        <ul className={classNames(cls.Listbox, {}, [])}>
          {isGroups ? (
            <>
              {filterItems.map((group, indexGroup) => {
                return (
                  <>
                    <li key={`groups-${indexGroup}`} className={cls.GroupLabel}>
                      {group?.label ?? ''}
                    </li>
                    {group?.children?.map((item, index) => {
                      const isFind = values.some((el) => el === item.value)

                      return (
                        <li
                          key={`combobox-autocomplete-${index}`}
                          onClick={() => {
                            setIsFocus(true)
                            inputRef.current && inputRef.current.focus()
                            setValues((prev) => {
                              const find = prev.some((el) => el === item.value)
                              if (find) {
                                return prev.filter((el) => el !== item.value)
                              } else {
                                return [...prev, item.value]
                              }
                            })
                            setSearch('')
                          }}
                          className={classNames(
                            cls.Item,
                            {
                              [cls.disabledItem]: item.disabled ?? false,
                            },
                            [],
                          )}
                        >
                          <span>{item.label}</span>
                          <div className={classNames(cls.IconContainer, {}, [])}>
                            {isFind && (
                              <div
                                className={classNames(
                                  '',
                                  {
                                    [cls.iconCheck]: isFind,
                                  },
                                  [],
                                )}
                              >
                                <CheckIcon fontSize='small' />
                              </div>
                            )}
                          </div>
                        </li>
                      )
                    })}
                  </>
                )
              })}
            </>
          ) : (
            <>
              {filterItems.map((item, index) => {
                const isFind = values.some((el) => el === item.value)

                return (
                  <li
                    key={`combobox-autocomplete-${index}`}
                    onClick={() => {
                      setIsFocus(true)
                      inputRef.current && inputRef.current.focus()
                      setValues((prev) => {
                        const find = prev.some((el) => el === item.value)
                        if (find) {
                          return prev.filter((el) => el !== item.value)
                        } else {
                          return [...prev, item.value]
                        }
                      })
                      setSearch('')
                    }}
                    className={classNames(
                      cls.Item,
                      {
                        [cls.disabledItem]: item.disabled ?? false,
                      },
                      [],
                    )}
                  >
                    <span>{item.label}</span>
                    <div className={classNames(cls.IconContainer, {}, [])}>
                      {isFind && (
                        <div
                          className={classNames(
                            '',
                            {
                              [cls.iconCheck]: isFind,
                            },
                            [],
                          )}
                        >
                          <CheckIcon fontSize='small' />
                        </div>
                      )}
                    </div>
                  </li>
                )
              })}
            </>
          )}
        </ul>
      ) : null}
    </div>
  )
}
