.Root {
  font-size: 14px;
}

.Label {
  padding: 0 0 4px;
  line-height: 1.5;
  display: block;
}

.InputWrapper {
  width: 100%;
  border: 1px solid var(--primary-color);
  background-color: var(--background-color-secondary);
  border-radius: 6px;
  padding: 1px;
  display: flex;
  flex-wrap: wrap;

  &:hover {
    border-color: var(--primary-color);
  }

  &.focused {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
  }

  & input {
    background-color: var(--background-color-secondary);
    color: var(--primary-color);
    height: 20px;
    box-sizing: border-box;
    padding: 4px 6px;
    //width: 0;
    min-width: 30px;
    flex-grow: 1;
    border: 0;
    margin: 0;
    outline: 0;
    width: 100%;
  }
}

.Listbox {
  //width: 300px;
  width: auto;
  margin: 2px 0 0;
  padding: 0;
  position: absolute;
  list-style: none;
  background-color: var(--background-color-secondary);
  overflow: auto;
  max-height: 250px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  z-index: 1;

  & li {
    padding: 5px 12px;
    display: flex;
    &:hover {
      background-color: var(--primary-color);
      color: var(--primary-color-invert);
      cursor: pointer;

      & svg {
        color: var(--primary-color-invert);
      }
    }

    & span {
      flex-grow: 1;
    }

    //& svg {
    //  color: transparent;
    //}
  }

  & li[aria-selected="true"] {
    background-color: var(--background-color-secondary);
    font-weight: 600;

    svg {
      color: #1890ff;
    }
  }

  & li.focused {
    background-color: var(--background-color-secondary);
    cursor: pointer;

    //& svg {
    //  color: red;
    //}
  }
}

.iconCheck {
  color: var(--primary-color);
}
.IconContainer {
  width: 20px;
  height: 20px;
  max-width: 20px;
  max-height: 20px;
  min-width: 20px;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.ChooseContainer {
  display: flex;
  align-items: center;
  justify-content: center;
}

.CloseIcon {
  stroke: var(--primary-color);
  stroke-width: 0.5;
}

.Item {
}
.disabledItem {
  background-color: var(--gray-background);
  &:hover {
    background-color: var(--gray-background) !important;
    color: var(--text-color) !important;
    cursor: default !important;
  }
}

.GroupLabel {
  font-size: 0.6rem;
  font-style: italic;
  cursor: default !important;
  height: 18px;
  display: flex;
  align-items: center;
  font-weight: bold;
  background-color: var(--gray-background);
  &:hover {
    background-color: var(--gray-background) !important;
    color: var(--text-color) !important;
  }
}
