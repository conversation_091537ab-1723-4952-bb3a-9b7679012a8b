.Table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.LoaderContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.isOverFlowVisible {
  overflow: visible !important;
}

.Head {
  width: 100%;
}

.Footer {
  height: 50px;
  width: 100%;
}

.Body {
  width: 100%;
  height: 100%;
  padding-top: 0.2rem;
}

.Paper {
  box-shadow: none !important;
  height: 100%;
  position: relative;

  th {
    background-color: var(--background-color-primary);
    border-bottom: solid 1px var(--row-color-gray);
    border-right: solid 2px var(--gray-background) !important;
  }

  td {
    position: relative;
    border-right: solid 1px var(--row-color-gray);
  }

  tr {
    &:hover {
      background-color: var(--gray-background) !important;

      td {
        background-color: var(--gray-background) !important;

        & > div {
          background-color: var(--gray-background) !important;
        }
      }
    }
  }
}

.HeaderIcons {
  flex: 0 1 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &Hidden {
    display: none;
  }
}

.TableHeaderRowContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.TableHeaderTdContent {
  width: 100%;
  display: flex;

  &Title {
    flex: 1 0 auto;

    &Center {
      text-align: center;
    }

    &Right {
      text-align: right;
    }
  }
}

.ArrowContainer {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  width: 16px;
  height: 20px;
}

.SelectSort {
  color: var(--primary-color);
}

.FilterItem {
  font-size: 0.75rem !important;
  cursor: default !important;

  &:hover {
    background-color: transparent !important;
  }

  span[class*='MuiFormControlLabel-label'] {
    font-size: inherit !important;
  }
}

.Arrow {
  height: 10px;
  width: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.isForceSearch {
  cursor: pointer;
}

.SearchContainer {
  height: 20px;
  width: 20px;
  min-width: 20px !important;
  min-height: 20px !important;
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.CustomTableTdCell {
  padding: 0 !important;
  width: 100%;
}

.CellLabelTree {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.isNotChilds {
  margin-left: 20px;
}

.ExpandedIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  margin: 0 2px;
  svg {
    width: 16px !important;
    height: 16px !important;
  }
}

.Visible {
  border: solid 1px var(--text-gray);
  cursor: pointer;
}

.CustomTableCell {
  height: 100%;
  font-size: 14px;
}

.Cell {
  width: 100%;
  height: 100%;
  min-width: 100%;
  min-height: 24px;
  padding: 0 !important;
  display: flex;
  align-items: center;
  font-size: 14px;
  white-space: pre; // Отображаем все пробелы
}

.CellSwitch {
  justify-content: center;
}

.Editable {
  border-radius: 6px;
  cursor: pointer;
  border: solid 1px transparent;

  &:hover {
    border: solid 1px var(--primary-color);
    cursor: pointer !important;
  }
}

// TODO: Разрабораться с пустыми классами
.NoEdit {
}

.Edited {
}

.Loader {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color-secondary);
  z-index: 999999;
}

.Empty {
  background-color: var(--background-color-secondary);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-color);
}

.GrayRow {
  background-color: var(--row-color-gray) !important;
}

.isGrayAndBoldColor {
  background-color: var(--row-color-gray) !important;
  font-weight: bold !important;
  font-style: italic;
}

.isGrayColorAndItalic {
  background-color: var(--row-color-gray) !important;
  font-style: italic;
}

.DatePicker > div > fieldset {
  padding: 0;
  border-color: transparent;
}

.DatePicker {
  height: 20px !important;
  border-radius: 4px;

  input {
    height: 20px;
    padding: 0 1em !important;
  }

  svg {
    width: 16px !important;
    height: 16px !important;
  }

  button {
    width: 16px !important;
    height: 16px !important;
  }
}

.DatePicker > div > input {
  padding: 0;
}

.EditCell {
  display: flex;
  align-items: center;
  width: 100%;
  > div {
    width: inherit;
  }

  &DatePicker {
    > div {
      max-width: 140px;
    }
  }
}

.EditCellSwitch {
  justify-content: center;
}

.CloseIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #757575 !important;
  width: 20px !important;
  min-width: 20px !important;
  height: 20px !important;
  padding: 0 !important;

  &:hover {
    color: var(--red-color) !important;
  }
}

.TextEdit {
  width: 100%;

  & > div {
    height: 20px !important;
  }
}

.Select {
  height: 20px;
  width: 100% !important;

  & > div > div {
    padding: 0 12px !important;
  }
}

.CheckboxContainer {
  width: 100%;
  height: 100%;
  min-width: 100%;
  min-height: 20px;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 1px;
}

.EmptyCellChecked {
  width: 24px;
  height: 24px;
}

.Table th [class*='Mui-disabled'] {
  display: none;
}

.Table tfoot td {
  font-weight: bold;
  color: var(--text-color);
}
.Table th {
  font-weight: bold;
}

.error {
  border: 1px solid var(--red-color);
}

.headerButton {
  stroke: none;
  display: block !important;
}
