import ContentCopy from '@mui/icons-material/ContentCopy'
import { Menu, MenuItem } from '@mui/material'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import React, { useState } from 'react'

import cls from './ContextMenu.module.scss'

export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState<{
    mouseX: number
    mouseY: number
  } | null>(null)

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault()
    setContextMenu(
      contextMenu === null
        ? {
            mouseX: event.clientX + 2,
            mouseY: event.clientY - 6,
          }
        : // repeated contextmenu when it is already open closes it with Chrome 84 on Ubuntu
          // Other native context menus might behave different.
          // With this behavior we prevent contextmenu from the backdrop to re-locale existing context menus.
          null,
    )
  }

  const handleClose = () => {
    setContextMenu(null)
  }

  return {
    contextMenuItem: (value?: string | null) => {
      if (!value) return null

      return (
        <Menu
          open={contextMenu !== null}
          onClose={handleClose}
          anchorReference='anchorPosition'
          anchorPosition={contextMenu !== null ? { top: contextMenu.mouseY, left: contextMenu.mouseX } : undefined}
        >
          <MenuItem
            onClick={() => {
              navigator.clipboard.writeText(value)
              handleClose()
            }}
          >
            <ListItemIcon className={cls.CopyIcon}>
              <ContentCopy />
            </ListItemIcon>
            <ListItemText className={cls.CopyText}>Копировать</ListItemText>
          </MenuItem>
        </Menu>
      )
    },
    handleContextMenu,
  }
}
