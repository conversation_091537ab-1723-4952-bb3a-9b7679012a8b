import { useStore } from 'stores/useStore.ts'
import { SpreadsheetReactProps } from 'widgets/SpreadsheetReact/SpreadsheetReact.tsx'

export const useSpreadsheetHelpers = () => {
  const { notificationStore } = useStore()

  const beforeCancelChange: SpreadsheetReactProps['beforeChange'] = (changes) => {
    const cancelChange = changes.some((cellChange) => {
      if (cellChange === null) return true
      const [, , , newValue] = cellChange

      return String(newValue).includes('=')
    })
    if (cancelChange) {
      notificationStore.addNotification({
        title: 'Ошибка',
        description: 'Копирование формул запрещено',
        type: 'warning',
      })
    }
  }

  return {
    beforeCancelChange,
  }
}
