import { CellValue } from 'handsontable/common'

export const validateAvrchmNorm = (value?: number | string, minNorm?: number) => {
  if (value === '' || value === undefined || value === null || isNaN(Number(value)) || minNorm === undefined) {
    return ''
  }
  if (Number(value) < minNorm) {
    return 'Значение меньше нормативного'
  }

  return ''
}

// Виды ошибок
// https://hyperformula.handsontable.com/guide/types-of-errors.html
export const validateSpreadsheetFormula = (value: CellValue) => {
  const errorRegex = new RegExp(/^#[A-Za-z0-9/]+/)
  if (errorRegex.test(value)) {
    return 'Некорректная формула'
  }

  return ''
}
