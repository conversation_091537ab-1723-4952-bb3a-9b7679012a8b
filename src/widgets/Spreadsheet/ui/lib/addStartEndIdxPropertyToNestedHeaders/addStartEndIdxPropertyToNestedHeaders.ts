import type { GridSettings } from 'handsontable/settings'

/**
 * Тип, представляющий вложенный заголовок таблицы без строковых значений.
 */
export type NestedHeader = Exclude<NonNullable<GridSettings['nestedHeaders']>[0][0], string>

/**
 * Интерфейс, расширяющий `NestedHeader`, добавляя индексы начального (`startColIdx`)
 * и конечного (`endColIdx`) столбцов, которые охватывает заголовок.
 */
export interface NestedHeaderWithColumnIdx extends NestedHeader {
  startColIdx: number
  endColIdx: number
}

/**
 * Добавляет свойства `startColIdx` и `endColIdx` к каждому элементу массива вложенных заголовков.
 * Эти индексы обозначают начальный и конечный столбцы, охватываемые каждым заголовком.
 *
 * @param {NestedHeader[][]} nestedHeaders - Двумерный массив заголовков таблицы, где каждый заголовок имеет `colspan`.
 * @returns {NestedHeaderWithColumnIdx[][]} - Двумерный массив заголовков с добавленными индексами `startColIdx` и `endColIdx`.
 */
export const addStartEndIdxPropertyToNestedHeaders = (
  nestedHeaders: NestedHeader[][],
): NestedHeaderWithColumnIdx[][] => {
  return nestedHeaders.map((nestedHeader) =>
    nestedHeader.reduce((acc, el, idx, initialArr) => {
      // Если это первый элемент в строке заголовков, он начинается с 0
      if (acc.length === 0) {
        return [{ ...el, startColIdx: 0, endColIdx: el.colspan - 1 }]
      }
      // Если это второй элемент, его startColIdx определяется по `colspan` предыдущего элемента
      else if (acc.length === 1) {
        acc.push({
          ...el,
          startColIdx: initialArr[idx - 1].colspan,
          endColIdx: initialArr[idx - 1].colspan + el.colspan - 1,
        })
      }
      // Для всех остальных элементов вычисляем индексы на основе предыдущего элемента в `acc`
      else {
        acc.push({
          ...el,
          startColIdx: acc[idx - 1].startColIdx + initialArr[idx - 1].colspan,
          endColIdx: acc[idx - 1].startColIdx + initialArr[idx - 1].colspan + el.colspan - 1,
        })
      }

      return acc
    }, [] as NestedHeaderWithColumnIdx[]),
  )
}
