import { describe, expect, it } from 'vitest'

import {
  addStartEndIdxPropertyToNestedHeaders,
  NestedHeader,
  NestedHeaderWithColumnIdx,
} from './addStartEndIdxPropertyToNestedHeaders.ts'

const twoLevelNestedHeader: NestedHeader[][] = [
  [
    {
      label: 'Итог',
      colspan: 3,
    },
    {
      label: 'Результат',
      colspan: 3,
    },
  ],
  [
    {
      label: 'мин',
      colspan: 1,
    },
    {
      label: 'план',
      colspan: 1,
    },
    {
      label: 'макс',
      colspan: 1,
    },
    {
      label: 'объединенная',
      colspan: 2,
    },
    {
      label: 'мин',
      colspan: 1,
    },
  ],
]

const twoLevelNestedHeaderResult: NestedHeaderWithColumnIdx[][] = [
  [
    {
      label: 'Итог',
      colspan: 3,
      startColIdx: 0,
      endColIdx: 2,
    },
    {
      label: 'Результат',
      colspan: 3,
      startColIdx: 3,
      endColIdx: 5,
    },
  ],
  [
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 0,
      endColIdx: 0,
    },
    {
      label: 'план',
      colspan: 1,
      startColIdx: 1,
      endColIdx: 1,
    },
    {
      label: 'макс',
      colspan: 1,
      startColIdx: 2,
      endColIdx: 2,
    },
    {
      label: 'объединенная',
      colspan: 2,
      startColIdx: 3,
      endColIdx: 4,
    },
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 5,
      endColIdx: 5,
    },
  ],
]

export const simplePlantNestedHeaders: NestedHeader[][] = [
  [
    {
      label: 'Итог',
      colspan: 3,
    },
    {
      label: 'Результат',
      colspan: 2,
    },
    {
      label: 'Итог.ОГР',
      colspan: 3,
    },
    {
      label: 'Модес',
      colspan: 3,
    },
  ],
  [
    {
      label: 'Σ',
      colspan: 3,
    },
    {
      label: 'Σ',
      colspan: 2,
    },
    {
      label: 'Σ',
      colspan: 3,
    },
    {
      label: 'Σ',
      colspan: 3,
    },
  ],
  [
    {
      label: 'мин',
      colspan: 1,
    },
    {
      label: 'план',
      colspan: 1,
    },
    {
      label: 'макс',
      colspan: 1,
    },
    {
      label: 'мин',
      colspan: 1,
    },
    {
      label: 'макс',
      colspan: 1,
    },
    {
      label: 'мин',
      colspan: 1,
    },
    {
      label: 'план',
      colspan: 1,
    },
    {
      label: 'макс',
      colspan: 1,
    },
    {
      label: 'мин',
      colspan: 1,
    },
    {
      label: 'план',
      colspan: 1,
    },
    {
      label: 'макс',
      colspan: 1,
    },
  ],
  [
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
    },
  ],
]

const simplePlantNestedHeadersResult: NestedHeaderWithColumnIdx[][] = [
  [
    {
      label: 'Итог',
      colspan: 3,
      startColIdx: 0,
      endColIdx: 2,
    },
    {
      label: 'Результат',
      colspan: 2,
      startColIdx: 3,
      endColIdx: 4,
    },
    {
      label: 'Итог.ОГР',
      colspan: 3,
      startColIdx: 5,
      endColIdx: 7,
    },
    {
      label: 'Модес',
      colspan: 3,
      startColIdx: 8,
      endColIdx: 10,
    },
  ],
  [
    {
      label: 'Σ',
      colspan: 3,
      startColIdx: 0,
      endColIdx: 2,
    },
    {
      label: 'Σ',
      colspan: 2,
      startColIdx: 3,
      endColIdx: 4,
    },
    {
      label: 'Σ',
      colspan: 3,
      startColIdx: 5,
      endColIdx: 7,
    },
    {
      label: 'Σ',
      colspan: 3,
      startColIdx: 8,
      endColIdx: 10,
    },
  ],
  [
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 0,
      endColIdx: 0,
    },
    {
      label: 'план',
      colspan: 1,
      startColIdx: 1,
      endColIdx: 1,
    },
    {
      label: 'макс',
      colspan: 1,
      startColIdx: 2,
      endColIdx: 2,
    },
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 3,
      endColIdx: 3,
    },
    {
      label: 'макс',
      colspan: 1,
      startColIdx: 4,
      endColIdx: 4,
    },
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 5,
      endColIdx: 5,
    },
    {
      label: 'план',
      colspan: 1,
      startColIdx: 6,
      endColIdx: 6,
    },
    {
      label: 'макс',
      colspan: 1,
      startColIdx: 7,
      endColIdx: 7,
    },
    {
      label: 'мин',
      colspan: 1,
      startColIdx: 8,
      endColIdx: 8,
    },
    {
      label: 'план',
      colspan: 1,
      startColIdx: 9,
      endColIdx: 9,
    },
    {
      label: 'макс',
      colspan: 1,
      startColIdx: 10,
      endColIdx: 10,
    },
  ],
  [
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 0,
      endColIdx: 0,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 1,
      endColIdx: 1,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 2,
      endColIdx: 2,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 3,
      endColIdx: 3,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 4,
      endColIdx: 4,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 5,
      endColIdx: 5,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 6,
      endColIdx: 6,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 7,
      endColIdx: 7,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 8,
      endColIdx: 8,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 9,
      endColIdx: 9,
    },
    {
      label: 'ВСВГО-1',
      colspan: 1,
      startColIdx: 10,
      endColIdx: 10,
    },
  ],
]

describe('addStartEndIdxPropertyToNestedHeaders', () => {
  it('test case for empty nestedHeaders', () => {
    const result = addStartEndIdxPropertyToNestedHeaders([])
    expect(result).toStrictEqual([])
  })

  it('test case for two level nestedHeaders', () => {
    const result = addStartEndIdxPropertyToNestedHeaders(twoLevelNestedHeader)
    expect(result).toStrictEqual(twoLevelNestedHeaderResult)
  })

  it('test case for simple plant nestedHeaders', () => {
    const result = addStartEndIdxPropertyToNestedHeaders(simplePlantNestedHeaders)
    expect(result).toStrictEqual(simplePlantNestedHeadersResult)
  })
})
