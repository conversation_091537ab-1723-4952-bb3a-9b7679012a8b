import { IGetSpreadsheetData, IGetSpreadsheetDataColumn } from 'entities/shared/spreadsheetDataResponse.ts'
import Handsontable from 'handsontable'
import { CellValue } from 'handsontable/common'
import { DetailedSettings } from 'handsontable/plugins/nestedHeaders'

interface BaseCellData {
  value?: string | number | null
  formula?: string | null
}

type CellSettings = Exclude<Handsontable.GridSettings['cell'], undefined>[0]

type IColumnSettings<Column> = Handsontable.ColumnSettings & Column
type ICellSettings<Cell> = CellSettings & Cell
type INestedHeader<NestedHeaderData> = DetailedSettings & NestedHeaderData

/**
 * NestedHeaderData - по умолчанию пустой объект, так как в шапках не всегда передаются доп. параметры
 */
export interface SpreadsheetBaseProps<Column, Cell, NestedHeaderData = NonNullable<unknown>> {
  /** Конфигурация отображения колонок в шапке (редактируемые, только на чтение и т.д.) */
  columns: IColumnSettings<Column>[]
  /** Данные, которые отображения в шапке, включая доп. данные */
  nestedHeaders: Array<Array<INestedHeader<NestedHeaderData>>>
  /** Данных, которые отображаются в ячейках таблицы */
  data: CellValue[][]
  /** Конфигурация отображения ячеек таблицы (редактируемые, только на чтение, стили и т.д., включая доп. данные)  */
  cell: ICellSettings<Cell>[]
  /** Конфигурация отображения заголовков строк  */
  rowHeaders: string[]
}

/** Вспомогательная функция для рекурсивного преобразования колонок.
 *  Формируем массив объектов, где задаем единственный параметр data = title - колонка нижнего уровня.
 * */
const convertColumns = (columns: IGetSpreadsheetDataColumn[]): IColumnSettings<NonNullable<unknown>>[] => {
  return columns.flatMap((column) => {
    if (column.subColumns && column.subColumns.length > 0) {
      return convertColumns(column.subColumns)
    } else {
      return {}
    }
  })
}

/** Вспомогательная функция для рекурсивного преобразования заголовков с поддержкой colspan */
const convertNestedHeaders = <NestedHeaderData>(
  columns: IGetSpreadsheetDataColumn[],
): Array<Array<INestedHeader<NestedHeaderData>>> => {
  const headers: Array<Array<INestedHeader<NestedHeaderData>>> = []

  const traverse = (cols: IGetSpreadsheetDataColumn[], level = 0): number => {
    if (!headers[level]) headers[level] = []
    let colspan = 0
    cols.forEach(({ title, subColumns, ...rest }) => {
      const subColspan = subColumns ? traverse(subColumns, level + 1) : 1
      headers[level].push({
        label: title,
        colspan: subColspan,
        ...(rest ? rest : {}),
      } as INestedHeader<NestedHeaderData>)
      colspan += subColspan
    })

    return colspan
  }

  traverse(columns)

  return headers
}

export const convertSpreadsheetResponseToComponentProps = <
  CellData extends BaseCellData,
  NestedHeaderData = NonNullable<unknown>,
>(
  payload: IGetSpreadsheetData<CellData>,
): SpreadsheetBaseProps<NonNullable<unknown>, CellData, NestedHeaderData> => {
  const columns = convertColumns(payload.columns)
  const nestedHeaders = convertNestedHeaders<NestedHeaderData>(payload.columns)
  const data = payload.rows.map((row) =>
    row.cells.map((cell) => (cell?.value !== undefined ? cell?.value : cell?.formula)),
  )
  const cell: ICellSettings<CellData>[] = payload.rows.flatMap((row, rowIndex) =>
    row.cells.map((cellData, colIndex) => ({
      row: rowIndex,
      col: colIndex,
      ...cellData,
    })),
  )
  const rowHeaders = payload.rows.map((row) => row.title)

  return {
    columns,
    nestedHeaders,
    data,
    cell,
    rowHeaders,
  }
}
