export const formatGesCalculationNumericValue = (value: string) => {
  const validCharacters: string[] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.']
  const res: string[] = []
  const formattedValue = value.replace(',', '.')
  for (let i = 0; i < formattedValue.length; i++) {
    const el: string = formattedValue[i]
    if (validCharacters.includes(el)) {
      if (el === '-' && i !== 0) continue // Пропуск минуса, если он не в начале числа
      if (el === '.' && res.includes('.')) continue // Пропуск точки, если уже присутствует
      res.push(el)
    }
  }
  if (res.length === 1 && res[0] === '.') {
    return ''
  }

  return res.join('')
}
