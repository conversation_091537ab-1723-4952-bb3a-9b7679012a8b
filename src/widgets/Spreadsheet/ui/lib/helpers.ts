import Handsontable from 'handsontable'

export const calculateSpreadsheetValueByFormula = (ht: Handsontable, formula: string): number => {
  const hfPlugin = ht.getPlugin('formulas')
  const hfEngine = hfPlugin.engine
  if (hfEngine && typeof hfPlugin.sheetId === 'number') {
    return hfEngine.calculateFormula(formula, hfPlugin.sheetId) as number
  }

  return NaN
}

export const isCellValueNumeric = (value?: string | null | number | boolean) => {
  const regex = /^-?\d+([.,]\d+)?$/

  return regex.test(String(value))
}
