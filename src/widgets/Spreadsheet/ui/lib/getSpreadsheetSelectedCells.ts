import Handsontable from 'handsontable'

export interface SpreadsheetSelectedCells {
  row: number
  col: number
  row2: number
  col2: number
}

export const getSpreadsheetSelectedCells = (hot?: Handsontable | null): SpreadsheetSelectedCells[] => {
  const selectedRange = hot?.getSelectedRange()
  const transformedSelectedRange: SpreadsheetSelectedCells[] = []
  if (selectedRange?.length) {
    selectedRange.forEach((range) => {
      let row = range.from.row
      let col = range.from.col
      let row2 = range.to.row
      let col2 = range.to.col
      if (row2 < row) {
        const tmp = row
        row = row2
        row2 = tmp
      }
      if (col2 < col) {
        const tmp = col
        col = col2
        col2 = tmp
      }

      // Проверяем каждую ячейку на наличие слова disabled в поле className
      // Если находим, то выходим из цикла и начинаем следующую итерацию
      for (let r = row; r <= row2; r++) {
        for (let c = col; c <= col2; c++) {
          if (r < 0 || c < 0) {
            continue
          }
          const cell = hot?.getCellMeta(r, c)
          if (String(cell?.className).toLowerCase().includes('disabled')) {
            continue
          }
          transformedSelectedRange.push({ row: r, col: c, row2: r, col2: c })
        }
      }
    })
  }

  return transformedSelectedRange
}
