import Handsontable from 'handsontable'

import {
  addStartEndIdxPropertyToNestedHeaders,
  NestedHeader,
  NestedHeaderWithColumnIdx,
} from '../addStartEndIdxPropertyToNestedHeaders'

/**
 * Функция, которая принимает данные о колонках и возвращает название класса.
 */
type Rule = (columnsData: Handsontable.ColumnSettings[]) => string

/**
 * Тип, который может быть либо строкой (именем класса), либо функцией (правилом для генерации имени класса).
 */
type ClassName = string | Rule

/**
 * Конфигурационный объект для добавления классов к заголовкам.
 */
export interface IHeaderClassesConfigItem {
  key: RegExp // Регулярное выражение для поиска соответствующих заголовков.
  classNames: ClassName // Классы или правило для вычисления классов.
  children: IHeaderClassesConfigItem[] // Вложенные правила для дочерних элементов заголовков.
}

/**
 * Хранилище классов для заголовков, где ключ имеет формат `${level}-${startColIdx}-${endColIdx}`,
 * а значение — массив классов.
 */
export interface RangeOfColumnIdxByDeepChildrenAnalysis {
  [key: string]: ClassName[]
}

/**
 * Рекурсивная функция для поиска и привязки классов к заголовкам таблицы на основе переданной конфигурации.
 *
 * @param {NestedHeaderWithColumnIdx[][]} nestedHeaders - Двумерный массив вложенных заголовков с индексами.
 * @param {IHeaderClassesConfigItem[]} children - Конфигурация классов для заголовков.
 * @param {NestedHeaderWithColumnIdx | null} activeNestedHeader - Текущий активный заголовок (по умолчанию `null`).
 * @param {number} level - Уровень вложенности заголовков (по умолчанию `0`).
 * @returns {RangeOfColumnIdxByDeepChildrenAnalysis} - Объект, содержащий классы для соответствующих заголовков.
 */
export const getRangeOfColumnIdxByDeepChildrenAnalysis = (
  nestedHeaders: NestedHeaderWithColumnIdx[][],
  children: IHeaderClassesConfigItem[],
  activeNestedHeader: NestedHeaderWithColumnIdx | null = null,
  level: number = 0,
): RangeOfColumnIdxByDeepChildrenAnalysis => {
  const res: RangeOfColumnIdxByDeepChildrenAnalysis = {}

  // Если у конфигурации нет вложенных элементов, возвращаем пустой объект
  if (children.length === 0) return res

  children.forEach((childConfig) => {
    // Проходимся по заголовкам на текущем уровне вложенности
    nestedHeaders[level].forEach((headerData) => {
      // Проверяем, соответствует ли заголовок шаблону `key`
      // и находится ли он в пределах активного заголовка (если он задан)
      if (
        childConfig.key.test(headerData.label) &&
        (activeNestedHeader === null ||
          (headerData.startColIdx >= activeNestedHeader.startColIdx &&
            headerData.endColIdx <= activeNestedHeader.endColIdx))
      ) {
        // Получаем классы из вложенных заголовков
        const prevRes = getRangeOfColumnIdxByDeepChildrenAnalysis(
          nestedHeaders,
          childConfig.children,
          headerData,
          level + 1,
        )
        Object.entries(prevRes).forEach(([key, classNames]) => {
          if (res[key] !== undefined) {
            res[key].push(...classNames)
          } else {
            res[key] = classNames
          }
        })

        // Формируем ключ в формате `${level}-${startColIdx}-${endColIdx}`
        const key = `${level}-${headerData.startColIdx}-${headerData.endColIdx}`

        // Если уже есть классы для этого заголовка, добавляем новые
        if (res[key]) {
          res[key].push(childConfig.classNames)
        } else {
          res[key] = [childConfig.classNames]
        }
      }
    })
  })

  return res
}

/**
 * Хранилище классов заголовков, где ключ имеет формат `${level}-${col}`,
 * а значение — массив строк с названием класса.
 */
export interface MapOfNestedHeadersBasedOnConfig {
  [index: string]: string[]
}

/**
 * Генерирует карту классов заголовков на основе переданной конфигурации.
 *
 * @param {NestedHeader[][]} nestedHeaders - Двумерный массив заголовков таблицы.
 * @param {Handsontable.GridSettings['columns']} columns - Данные о колонках таблицы.
 * @param {IHeaderClassesConfigItem[]} headerClassesConfig - Конфигурация классов для заголовков.
 * @returns {MapOfNestedHeadersBasedOnConfig} - Объект с ключами в формате `${level}-${col}` и соответствующими классами.
 */
export const getMapOfNestedHeadersBasedOnConfig = (
  nestedHeaders: NestedHeader[][],
  columns: Handsontable.ColumnSettings[],
  headerClassesConfig: IHeaderClassesConfigItem[],
): MapOfNestedHeadersBasedOnConfig => {
  // Добавляем индексы начального и конечного столбцов для каждого заголовка
  const nestedHeadersWithStartAndEndIdx = addStartEndIdxPropertyToNestedHeaders(nestedHeaders)

  // Получаем данные о диапазонах колонок для добавления классов
  const rangeOfColumnIdxForAddingClassname = getRangeOfColumnIdxByDeepChildrenAnalysis(
    nestedHeadersWithStartAndEndIdx,
    headerClassesConfig,
  )

  // Создаем карту заголовков и их классов
  return Object.entries(rangeOfColumnIdxForAddingClassname).reduce((acc, [rangeOfColumnData, classNames]) => {
    const [level, startColIdx, endColIdx] = rangeOfColumnData.split('-')
    classNames.forEach((className) => {
      if (!acc[`${level}-${startColIdx}`]) {
        acc[`${level}-${startColIdx}`] = []
      }
      if (typeof className === 'string') {
        // Если className — строка, просто добавляем его
        acc[`${level}-${startColIdx}`].push(className)
      } else {
        // Если className — функция, применяем её к данным о колонках.
        // Увеличиваем `endColIdx` на 1 для корректной работы метода splice
        const endColIdxNum = Number(endColIdx) + 1

        // Применяем правило к соответствующему диапазону колонок
        const classNameFromValidator = className(columns.slice(Number(startColIdx), endColIdxNum))
        if (classNameFromValidator) {
          acc[`${level}-${startColIdx}`].push(classNameFromValidator)
        }
      }
    })

    return acc
  }, {} as MapOfNestedHeadersBasedOnConfig)
}
