import Handsontable from 'handsontable'
import { CellValue } from 'handsontable/common'

const SHORTCUT_GROUP_ID = 'distribute_values'

const baseShortcut = {
  group: SHORTCUT_GROUP_ID,
  keys: [
    ['Control', 'D'],
    ['Control', 'В'],
  ],
}

const copyLastSelectedValueToBottomRows = (
  hot: Handsontable,
  row: number,
  col: number,
  value: number | string | null,
) => {
  // Если ячейка заблокирована, то копирование значений запрещено
  const cell = hot?.getCellMeta(row, col)
  if (String(cell?.className).toLowerCase().includes('disabled')) return

  const totalRows = hot.countRows()
  const changes: Array<[number, number, CellValue]> = []

  // Проходимся по всем строкам включая ту, из которой копируется значение
  for (let r = row; r < totalRows; r++) {
    const cellBelow = hot.getCellMeta(r, col)

    // Заполняем массив данными для вставки только в том случае, если ячейка НЕ заблокирована
    if (
      !String(cellBelow?.className).toLowerCase().includes('disabled') &&
      !String(cellBelow?.className).includes('distributionProhibited')
    ) {
      changes.push([r, col, value])
    }
  }

  if (changes.length > 0) {
    hot.setDataAtCell(changes)
  }
}

/**
 * Копирование выделенной ячейки по нажатию на ctrl+d во все нижележащие ячейки, которые доступны для редактирования
 * и не имеют класса distributionProhibited
 */
const getShortcutForGrid = (hot: Handsontable) => ({
  ...baseShortcut,
  callback: (event: Event) => {
    event.preventDefault()
    const selectedCell = hot.getSelected()
    if (!selectedCell || selectedCell.length === 0) return

    // Если выделение в таблице происходило несколько раз, то
    const [startRow, startCol, endRow, endCol] = selectedCell[selectedCell.length - 1]
    // Если выделено более одной колонки, то не обрабатываем нажатие горячей клавиши
    if (startCol !== endCol) return

    // В рамках одного выделения выбираем наибольшую строку
    const lastSelectedRow = Math.max(startRow, endRow)
    const value = hot.getDataAtCell(lastSelectedRow, endCol)
    copyLastSelectedValueToBottomRows(hot, lastSelectedRow, endCol, value)
  },
})

/**
 * Копирование редактируемой ячейки по нажатию на ctrl+d во все нижележащие ячейки, которые доступны для редактирования
 * и не имеют класса distributionProhibited
 */
const getShortcutForEditor = (hot: Handsontable) => ({
  ...baseShortcut,
  callback: (event: Event) => {
    event.preventDefault()
    const selectedCell = hot.getSelected()
    if (!selectedCell || selectedCell.length === 0) return

    const [startRow, startCol] = selectedCell[0]
    copyLastSelectedValueToBottomRows(hot, startRow, startCol, (event.target as HTMLTextAreaElement).value)
  },
})

export { getShortcutForEditor, getShortcutForGrid, SHORTCUT_GROUP_ID }
