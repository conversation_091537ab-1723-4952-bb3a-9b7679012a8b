import { describe, expect, it } from 'vitest'

import { formatDate, formatDateTime, formatDaysToReadableStr } from './dateFormates'

describe('formatDateTime', () => {
  // Should format a valid date string to the expected format
  it('should format a valid date string to the expected format', () => {
    const dateTime = '2022-01-01T12:34:56'
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should format a valid Date object to the expected format
  it('should format a valid Date object to the expected format', () => {
    const dateTime = new Date('2022-01-01T12:34:56')
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should format a date string with leading zeros to the expected format
  it('should format a date string with leading zeros to the expected format', () => {
    const dateTime = '2022-01-01T02:04:06'
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 02:04:06')
  })

  // Should format a date string with trailing spaces to the expected format
  it('should format a date string with trailing spaces to the expected format', () => {
    const dateTime = '2022-01-01T12:34:56   '
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should format a date string with a different time zone to the expected format
  it('should format a date string with a different time zone to the expected format', () => {
    const dateTime = '2022-01-01T12:34:56+03:00'
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should format a date string with a different locale to the expected format
  it('should format a date string with a different locale to the expected format', () => {
    const dateTime = '2022-01-01T12:34:56'
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should throw an error when given an invalid date string
  it('should throw an error when given an invalid date string', () => {
    const dateTime = 'invalid-date-string'
    expect(() => formatDateTime(dateTime)).toThrow()
  })

  // Should throw an error when given an invalid Date object
  it('should throw an error when given an invalid Date object', () => {
    const dateTime = new Date('invalid-date-string')
    expect(() => formatDateTime(dateTime)).toThrow()
  })

  // Should format a date string with an invalid format to the expected format
  it('should format a date string with an invalid format to the expected format', () => {
    const dateTime = '01-01-2022 12:34:56'
    const result = formatDateTime(dateTime)
    expect(result).toBe('01.01.2022 12:34:56')
  })

  // Should format a date string with a year greater than 9999 to the expected format
  it('should format a date string with a year greater than 9999 to the expected format', () => {
    const dateTime = '10000-01-01T12:34:56'
    // const result = formatDateTime(dateTime);
    // expect(result).toBe('01.01.10000 12:34:56');
    expect(() => formatDateTime(dateTime)).toThrow('Invalid time value')
  })

  // Should format a date string with a year less than 1 to the expected format
  it('should format a date string with a year less than 1 to the expected format', () => {
    const dateTime = '0000-01-01T12:34:56'
    expect(() => formatDateTime(dateTime)).toThrow('Invalid time value')
  })

  // Should handle leap years correctly
  it('should handle leap years correctly', () => {
    const dateTime = '2024-02-29T12:34:56'
    const result = formatDateTime(dateTime)
    expect(result).toBe('29.02.2024 12:34:56')
  })
})
describe('formatDate', () => {
  // Should format a valid date string in the format 'dd.MM.yyyy'
  it('should format a valid date string in the format dd.MM.yyyy', () => {
    const result = formatDate('2022-01-01')
    expect(result).toBe('01.01.2022')
  })

  // Should format a valid Date object in the format 'dd.MM.yyyy'
  it('should format a valid Date object in the format dd.MM.yyyy', () => {
    const result = formatDate(new Date('2022-01-01'))
    expect(result).toBe('01.01.2022')
  })

  // Should format a date string with leading zeros in the day and month fields
  it('should format a date string with leading zeros in the day and month fields', () => {
    const result = formatDate('2022-01-01')
    expect(result).toBe('01.01.2022')
  })

  // Should format a date string without leading zeros in the day and month fields
  it('should format a date string without leading zeros in the day and month fields', () => {
    const result = formatDate('2022-1-1')
    expect(result).toBe('01.01.2022')
  })

  // Should format a date string with a single digit day and month
  it('should format a date string with a single digit day and month', () => {
    const result = formatDate('2022-2-3')
    expect(result).toBe('03.02.2022')
  })

  // Should format a date string with a four digit year
  it('should format a date string with a four digit year', () => {
    const result = formatDate('2022-12-31')
    expect(result).toBe('31.12.2022')
  })

  // Should throw an error when given an invalid date string
  it('should throw an error when given an invalid date string', () => {
    expect(() => {
      formatDate('invalid-date')
    }).toThrow()
  })

  // Should throw an error when given an invalid Date object
  it('should throw an error when given an invalid Date object', () => {
    expect(() => {
      formatDate(new Date('invalid-date'))
    }).toThrow()
  })
})
describe('formatDaysToReadableStr', () => {
  // Returns a string with the correct format for a positive number of days ending in 1
  it('should return the correct format for a positive number of days ending in 1', () => {
    const result = formatDaysToReadableStr(1)
    expect(result).toBe('1 день')
  })

  // Returns a string with the correct format for a positive number of days ending in 2, 3 or 4
  it('should return the correct format for a positive number of days ending in 2, 3 or 4', () => {
    const result = formatDaysToReadableStr(2)
    expect(result).toBe('2 дня')
  })

  // Returns a string with the correct format for a positive number of days ending in 0 or 5-9
  it('should return the correct format for a positive number of days ending in 0 or 5-9', () => {
    const result = formatDaysToReadableStr(5)
    expect(result).toBe('5 дней')
  })

  // Returns a string with the correct format for a positive number of days ending in 11-19
  it('should return the correct format for a positive number of days ending in 11-19', () => {
    const result = formatDaysToReadableStr(15)
    expect(result).toBe('15 дней')
  })

  // Returns "Отрицательное количество дней" for a negative number of days
  it('should return "Отрицательное количество дней" for a negative number of days', () => {
    const result = formatDaysToReadableStr(-5)
    expect(result).toBe('Отрицательное количество дней')
  })

  // Returns a string with the correct format for the number 0
  it('should return the correct format for the number 0', () => {
    const result = formatDaysToReadableStr(0)
    expect(result).toBe('0 дней')
  })

  // Returns a string with the correct format for the number 10
  it('should return the correct format for the number 10', () => {
    const result = formatDaysToReadableStr(10)
    expect(result).toBe('10 дней')
  })

  // Returns a string with the correct format for the number 20
  it('should return the correct format for the number 20', () => {
    const result = formatDaysToReadableStr(20)
    expect(result).toBe('20 дней')
  })

  // Returns a string with the correct format for the number 100
  it('should return the correct format for the number 100', () => {
    const result = formatDaysToReadableStr(100)
    expect(result).toBe('100 дней')
  })

  // Returns a string with the correct format for the number 111
  it('should return the correct format for the number 111', () => {
    const result = formatDaysToReadableStr(111)
    expect(result).toBe('111 дней')
  })

  // Returns a string with the correct format for the number 123
  it('should return the correct format for the number 123', () => {
    const result = formatDaysToReadableStr(123)
    expect(result).toBe('123 дня')
  })

  // Returns a string with the correct format for the number 999
  it('should return the correct format for the number 999', () => {
    const result = formatDaysToReadableStr(999)
    expect(result).toBe('999 дней')
  })
})
