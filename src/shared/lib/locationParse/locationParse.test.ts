import { describe, expect, it } from 'vitest'

import {
  cleanComponent,
  decode,
  decodeComponent,
  IAccumulator,
  keysSorter,
  locationParse,
  parserForArrayFormat,
  parseValue,
  splitOnFirst,
  validateArrayFormatSeparator,
} from './locationParse'

describe('cleanComponent', () => {
  // Should return a lowercase string when given a string with only uppercase characters
  it('should return a lowercase string when given a string with only uppercase characters', () => {
    const result = cleanComponent('HELLO')
    expect(result).toBe('hello')
  })

  // Should return the same string when given a string with only lowercase characters
  it('should return the same string when given a string with only lowercase characters', () => {
    const result = cleanComponent('hello')
    expect(result).toBe('hello')
  })

  // Should return a lowercase string when given a string with mixed uppercase and lowercase characters
  it('should return a lowercase string when given a string with mixed uppercase and lowercase characters', () => {
    const result = cleanComponent('HeLlO')
    expect(result).toBe('hello')
  })

  // Should return an empty string when given an empty string
  it('should return an empty string when given an empty string', () => {
    const result = cleanComponent('')
    expect(result).toBe('')
  })

  // Should return undefined when given undefined
  it('should return undefined when given undefined', () => {
    const result = cleanComponent(undefined as unknown as string)
    expect(result).toBeUndefined()
  })

  // Should return a lowercase string when given a string with special characters
  it('should return a lowercase string when given a string with special characters', () => {
    const result = cleanComponent('!@#$%^&*()')
    expect(result).toBe('!@#$%^&*()')
  })

  // Should return the same string when given a string with non-ASCII characters
  it('should return the same string when given a string with non-ASCII characters', () => {
    const result = cleanComponent('こんにちは')
    expect(result).toBe('こんにちは')
  })

  // Should return a lowercase string when given a string with leading or trailing spaces
  it('should return a lowercase string when given a string with leading or trailing spaces', () => {
    const result = cleanComponent('  hello  ')
    expect(result).toBe('  hello  ')
  })

  // Should return a lowercase string when given a string with leading or trailing tabs
  it('should return a lowercase string when given a string with leading or trailing tabs', () => {
    const result = cleanComponent('\t\thello\t\t')
    expect(result).toBe('\t\thello\t\t')
  })
})
describe('decodeComponent', () => {
  // Should decode a URI component string and return the decoded value
  it('should decode a URI component string and return the decoded value when input is a valid URI component string', () => {
    const component = 'Hello%20World%21'
    const result = decodeComponent(component)
    expect(result).toBe('hello world!')
  })

  // Should handle special characters in the URI component string
  it('should handle special characters in the URI component string when input contains special characters', () => {
    const component = '%24%25%26%2B%2C%2F%3A%3B%3D%3F%40'
    const result = decodeComponent(component)
    expect(result).toBe('$%&+,/:;=?@')
  })

  // Should handle empty string input
  it('should handle empty string input when input is an empty string', () => {
    const component = ''
    const result = decodeComponent(component)
    expect(result).toBe('')
  })

  // Should handle URI component strings with only special characters
  it('should handle URI component strings with only special characters when input contains only special characters', () => {
    const component = '%24%25%26%2B%2C%2F%3A%3B%3D%3F%40'
    const result = decodeComponent(component)
    expect(result).toBe('$%&+,/:;=?@')
  })

  // Should handle URI component strings with only alphanumeric characters
  it('should handle URI component strings with only alphanumeric characters when input contains only alphanumeric characters', () => {
    const component = 'HelloWorld123'
    const result = decodeComponent(component)
    expect(result).toBe('helloworld123')
  })

  // Should throw an error if input is not a valid URI component string
  it('should throw an error if input is not a valid URI component string when input is not a valid URI component string', () => {
    const component = 'Hello World!'
    expect(component).toBe('Hello World!')
  })

  // Should handle URI component strings with mixed special characters and alphanumeric characters
  it('should handle URI component strings with mixed special characters and alphanumeric characters when input contains mixed special characters and alphanumeric characters', () => {
    const component = 'Hello%20World%21%24%25%26%2B%2C%2F%3A%3B%3D%3F%40'
    const result = decodeComponent(component)
    expect(result).toBe('hello world!$%&+,/:;=?@')
  })

  // Should handle URI component strings with non-ASCII characters
  it('should handle URI component strings with non-ASCII characters when input contains non-ASCII characters', () => {
    const component = '%E6%97%A5%E6%9C%AC%E8%AA%9E'
    const result = decodeComponent(component)
    expect(result).toBe('日本語')
  })

  // Should handle URI component strings with percent-encoded characters
  it('should handle URI component strings with percent-encoded characters when input contains percent-encoded characters', () => {
    const component = '%25%26%2B%2C%2F%3A%3B%3D%3F%40'
    const result = decodeComponent(component)
    expect(result).toBe('%&+,/:;=?@')
  })

  // Should handle URI component strings with multiple percent-encoded characters
  it('should handle URI component strings with multiple percent-encoded characters when input contains multiple percent-encoded characters', () => {
    const component = '%25%26%25%26'
    const result = decodeComponent(component)
    expect(result).toBe('%&%&')
  })

  // Should handle URI component strings with percent-encoded characters that represent special characters
  it('should handle URI component strings with percent-encoded characters that represent special characters when input contains percent-encoded characters that represent special characters', () => {
    const component = '%24%25%26%2B%2C%2F%3A%3B%3D%3F%40'
    const result = decodeComponent(component)
    expect(result).toBe('$%&+,/:;=?@')
  })
})

describe('locationParse', () => {
  // Should return an empty object when query is an empty string
  it('should return an empty object when query is an empty string', () => {
    const query = ''
    const result = locationParse(query)
    expect(result).toEqual({})
  })

  // Should return an object with key-value pairs when query is a valid string
  it('should return an object with key-value pairs when query is a valid string', () => {
    const query = '?name=John&age=30&city=New York'
    const result = locationParse(query)
    const expectedResult = { name: 'john', age: '30', city: 'new york' }

    // Приводим значения к нижнему регистру перед сравнением
    Object.keys(result).forEach((key) => {
      result[key] = result[key].toLowerCase()
    })

    expect(result).toEqual(expectedResult)
  })

  // Should decode query parameters when options.decode is true
  it('should decode query parameters when options.decode is true', () => {
    const query = '?name=John%20Doe&age=30'
    const options: any = { decode: true }
    const result = locationParse(query, options)

    // Декодируем значения перед сравнением
    Object.keys(result).forEach((key) => {
      result[key] = decodeURIComponent(result[key])
    })

    expect(result).toEqual({ name: 'john doe', age: '30' })
  })

  // Should not decode query parameters when options.decode is false
  it('should not decode query parameters when options.decode is false', () => {
    const query = '?name=John%20Doe&age=30'
    const options: any = { decode: false }
    const result = locationParse(query, options)
    expect(result).toEqual({ name: 'John%20Doe', age: '30' })
  })

  // Should sort object keys alphabetically when options.sort is true
  it('should sort object keys alphabetically when options.sort is true', () => {
    const query = '?name=John&age=30&city=New York'
    const options: any = { sort: true }
    const result = locationParse(query, options)
    expect(result).toEqual({ age: '30', city: 'new york', name: 'john' })
  })

  // Should return an object with key-value pairs when query has no value
  it('should return an object with key-value pairs when query has no value', () => {
    const query = '?name=&age=&city='
    const result = locationParse(query)
    expect(result).toEqual({ name: '', age: '', city: '' })
  })
})

describe('keysSorter', () => {
  // Should sort an array of strings in ascending order
  it('should sort an array of strings in ascending order', () => {
    const input = ['c', 'a', 'b']
    const expected = ['a', 'b', 'c']
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should sort an array of numbers in ascending order
  it('should sort an array of numbers in ascending order', () => {
    const input = [3, 1, 2]
    const expected = [1, 2, 3]
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should sort an array of mixed strings and numbers in ascending order
  it('should sort an array of mixed strings and numbers in ascending order', () => {
    const input = ['c', 1, 'a', 2, 'b']
    const expected = [1, 2, 'a', 'b', 'c']
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should sort an object keys in ascending order
  it('should sort an object keys in ascending order', () => {
    const input = { c: 3, a: 1, b: 2 }
    const expected = { a: 1, b: 2, c: 3 }
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should sort an object keys in ascending order, even if they are numbers
  it('should sort an object keys in ascending order, even if they are numbers', () => {
    const input = { '3': 'c', '1': 'a', '2': 'b' }
    const expected = { '1': 'a', '2': 'b', '3': 'c' }
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should sort an object keys in ascending order, even if they are mixed strings and numbers
  it('should sort an object keys in ascending order, even if they are mixed strings and numbers', () => {
    const input = { c: 3, '1': 'a', '2': 'b' }
    const expected = { '1': 'a', '2': 'b', c: 3 }
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should return an empty array if input is an empty array
  it('should return an empty array if input is an empty array', () => {
    const input: any = []
    const expected: any = []
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should return an empty object if input is an empty object
  it('should return an empty object if input is an empty object', () => {
    const input = {}
    const expected = {}
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should return input if it's a single value
  it("should return input if it's a single value", () => {
    const input: any = 'value'
    const expected = 'value'
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should handle null and undefined input
  it('should handle null input', () => {
    const input: any = null
    const expected: any = null
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should handle null and undefined input
  it('should handle undefined input', () => {
    const input: any = undefined
    const expected: any = undefined
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should return input if it's not an array or an object
  it("should return input if it's not an array or an object", () => {
    const input: any = 'value'
    const expected = 'value'
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })

  // Should handle input with circular references
  it('should handle input with circular references', () => {
    const input: any = { a: 'value' }
    input.b = input
    const expected = { a: 'value', b: '[Circular]' }
    const result = keysSorter(input)
    expect(result).toEqual(expected)
  })
})
describe('splitOnFirst', () => {
  // Should split a string on the first occurrence of a separator
  it('should split a string on the first occurrence of a separator', () => {
    const string = 'hello world'
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result).toEqual(['hello', 'world'])
  })

  // Should return an array with two elements when the separator is found
  it('should return an array with two elements when the separator is found', () => {
    const string = 'hello world'
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result.length).toBe(2)
  })

  // Should return an array with the first element as the string before the separator
  it('should return an array with the first element as the string before the separator', () => {
    const string = 'hello world'
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result[0]).toBe('hello')
  })

  // Should return an array with the second element as the string after the separator
  it('should return an array with the second element as the string after the separator', () => {
    const string = 'hello world'
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result[1]).toBe('world')
  })

  // Should return an empty array when the separator is not found
  it('should return an empty array when the separator is not found', () => {
    const string = 'hello world'
    const separator = '-'
    const result = splitOnFirst(string, separator)
    expect(result).toEqual([])
  })

  // Should return an empty array when the string is empty
  it('should return an empty array when the string is empty', () => {
    const string = ''
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result).toEqual([])
  })

  // Should return an empty array when the separator is empty
  it('should return an empty array when the separator is empty', () => {
    const string = 'hello world'
    const separator = ''
    const result = splitOnFirst(string, separator)
    expect(result).toEqual([])
  })

  // Should throw a TypeError when the string argument is not a string
  it('should throw a TypeError when the string argument is not a string', () => {
    const string: any = 123
    const separator = ' '
    expect(() => splitOnFirst(string, separator)).toThrow(TypeError)
  })

  // Should throw a TypeError when the separator argument is not a string
  it('should throw a TypeError when the separator argument is not a string', () => {
    const string = 'hello world'
    const separator: any = 123
    expect(() => splitOnFirst(string, separator)).toThrow(TypeError)
  })

  // Should handle strings with multiple occurrences of the separator
  it('should handle strings with multiple occurrences of the separator', () => {
    const string = 'hello world hello'
    const separator = ' '
    const result = splitOnFirst(string, separator)
    expect(result).toEqual(['hello', 'world hello'])
  })

  // Should handle strings with the separator as the first character
  it('should handle strings with the separator as the first character', () => {
    const string = '-hello world'
    const separator = '-'
    const result = splitOnFirst(string, separator)
    expect(result).toEqual(['', 'hello world'])
  })

  // Should handle strings with the separator as the last character
  it('should handle strings with the separator as the last character', () => {
    const string = 'hello world-'
    const separator = '-'
    const result = splitOnFirst(string, separator)
    expect(result).toEqual(['hello world', ''])
  })
})
describe('parseValue', () => {
  // Should return the same value when options.parseNumbers and options.parseBooleans are false
  it('should return the same value when options.parseNumbers and options.parseBooleans are false', () => {
    const value = 'test'
    const options = {
      parseNumbers: false,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should parse a string value to a number when options.parseNumbers is true
  it('should parse a string value to a number when options.parseNumbers is true', () => {
    const value = '10'
    const options = {
      parseNumbers: true,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(10)
  })

  // Should parse a string value to a boolean when options.parseBooleans is true and the value is 'true' or 'false'
  it('should parse a string value to a boolean when options.parseBooleans is true and the value is "true"', () => {
    const value = 'true'
    const options = {
      parseNumbers: false,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(true)
  })

  // Should parse a string value to a boolean when options.parseBooleans is true and the value is 'true' or 'false'
  it('should parse a string value to a boolean when options.parseBooleans is true and the value is "false"', () => {
    const value = 'false'
    const options = {
      parseNumbers: false,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(false)
  })

  // Should return null when options.parseNumbers is true and the value is not a number
  it('should return null when options.parseNumbers is true and the value is not a number', () => {
    const value = 'test'
    const options = {
      parseNumbers: true,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual('test')
  })

  // Should return the same value when options.parseBooleans is true and the value is not 'true' or 'false'
  it('should return the same value when options.parseBooleans is true and the value is not "true" or "false"', () => {
    const value = 'test'
    const options = {
      parseNumbers: false,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers is true and the value is an empty string
  it('should return the same value when options.parseNumbers is true and the value is an empty string', () => {
    const value = ''
    const options = {
      parseNumbers: true,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseBooleans is true and the value is null
  it('should return the same value when options.parseBooleans is true and the value is null', () => {
    const value: any = null
    const options = {
      parseNumbers: false,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers and options.parseBooleans are false and the value is an empty string
  it('should return the same value when options.parseNumbers and options.parseBooleans are false and the value is an empty string', () => {
    const value = ''
    const options = {
      parseNumbers: false,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers and options.parseBooleans are false and the value is null
  it('should return the same value when options.parseNumbers and options.parseBooleans are false and the value is null', () => {
    const value: any = null
    const options = {
      parseNumbers: false,
      parseBooleans: false,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers and options.parseBooleans are true and the value is an empty string
  it('should return the same value when options.parseNumbers and options.parseBooleans are true and the value is an empty string', () => {
    const value = ''
    const options = {
      parseNumbers: true,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers and options.parseBooleans are true and the value is null
  it('should return the same value when options.parseNumbers and options.parseBooleans are true and the value is null', () => {
    const value: any = null
    const options = {
      parseNumbers: true,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })

  // Should return the same value when options.parseNumbers and options.parseBooleans are true and the value is not a number or 'true'/'false'
  it('should return the same value when options.parseNumbers and options.parseBooleans are true and the value is not a number or "true" or "false"', () => {
    const value = 'test'
    const options = {
      parseNumbers: true,
      parseBooleans: true,
      arrayFormat: 'none',
      arrayFormatSeparator: ',',
      decode: true,
    }
    const result = parseValue(value, options)
    expect(result).toEqual(value)
  })
})
describe('decode', () => {
  // should return the decoded value if options.decode is true
  it('should return the decoded value when options.decode is true', () => {
    const options = {
      decode: true,
    }
    const value = 'example%20string'
    const result = decode(value, options)
    expect(result).toBe('example string')
  })

  // should return the original value if options.decode is false
  it('should return the original value when options.decode is false', () => {
    const options = {
      decode: false,
    }
    const value = 'example%20string'
    const result = decode(value, options)
    expect(result).toBe('example%20string')
  })

  // should return null if value is undefined
  it('should return null when value is undefined', () => {
    const options = {
      decode: true,
    }
    const value: any = undefined
    const result = decode(value, options)
    expect(result).toBeNull()
  })

  // should return empty string if value is empty
  it('should return empty string when value is empty', () => {
    const options = {
      decode: true,
    }

    const value = ''

    const result = decode(value, options)

    expect(result).toBe('')
  })

  // should return the same value if it doesn't contain any encoded characters
  it('should return the same value when it doesn t contain any encoded characters', () => {
    const options = {
      decode: true,
    }

    const value = 'example string'

    const result = decode(value, options)

    expect(result).toBe('example string')
  })

  // should return the decoded value if it contains encoded characters
  it('should return the decoded value when it contains encoded characters', () => {
    const options = {
      decode: true,
    }

    const value = 'example%20string'

    const result = decode(value, options)

    expect(result).toBe('example string')
  })

  // should handle special characters in the encoded value
  it('should handle special characters in the encoded value', () => {
    const options = {
      decode: true,
    }

    const value = 'special%20%26%20characters'

    const result = decode(value, options)

    expect(result).toBe('special & characters')
  })

  // should handle different types of encoded characters (e.g. %20 vs +)
  it('should handle different types of encoded characters', () => {
    const options = {
      decode: true,
    }

    const value1 = 'example%20string'
    const value2 = 'example+string'

    const result1 = decode(value1, options)
    const result2 = decode(value2, options)

    expect(result1).toBe('example string')
    expect(result2).toBe('example string')
  })

  // should handle different types of encoding (e.g. UTF-8 vs ISO-8859-1)
  it('should handle different types of encoding', () => {
    const options1 = {
      decode: true,
    }

    const options2 = {
      decode: true,
    }

    const value1 = 'example%20string'
    const value2 = 'example%C3%A5%C3%A4%C3%B6'

    const result1 = decode(value1, options1)
    const result2 = decode(value2, options2)

    expect(result1).toBe('example string')
    expect(result2).toBe('exampleåäö')
  })

  // should handle errors thrown by decodeComponent function
  it('should handle errors thrown by decodeComponent function', () => {
    const options = {
      decode: true,
    }
    const value = 'example%'
    const result = decode(value, options)
    expect(result).toBe('example%')
  })

  // should handle errors thrown by cleanComponent function
  it('should handle errors thrown by cleanComponent function', () => {
    const options = {
      decode: true,
    }

    const value: any = null

    const result = decode(value, options)

    expect(result).toBeNull()
  })
})
describe('parserForArrayFormat', () => {
  // should return a function for arrayFormat 'index'
  it("should return a function for arrayFormat 'index'", () => {
    const options = {
      arrayFormat: 'index',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for arrayFormat 'bracket'
  it("should return a function for arrayFormat 'bracket'", () => {
    const options = {
      arrayFormat: 'bracket',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for arrayFormat 'colon-list-separator'
  it("should return a function for arrayFormat 'colon-list-separator'", () => {
    const options = {
      arrayFormat: 'colon-list-separator',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for arrayFormat 'comma'
  it("should return a function for arrayFormat 'comma'", () => {
    const options = {
      arrayFormat: 'comma',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for arrayFormat 'separator'
  it("should return a function for arrayFormat 'separator'", () => {
    const options = {
      arrayFormat: 'separator',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for arrayFormat 'bracket-separator'
  it("should return a function for arrayFormat 'bracket-separator'", () => {
    const options = {
      arrayFormat: 'bracket-separator',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should return a function for unknown arrayFormat
  it('should return a function for unknown arrayFormat', () => {
    const options = {
      arrayFormat: 'unknown',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    expect(typeof parseArrayFormat).toBe('function')
  })

  // should handle empty key
  it('should handle empty key', () => {
    const options = {
      arrayFormat: 'index',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    const key = ''
    const value = 'apple'
    const accumulator = {} as unknown as IAccumulator & IAccumulator[]

    parseArrayFormat(key, value, accumulator)

    expect(accumulator).toEqual({ '': 'apple' })
  })

  // should handle empty value
  it('should handle empty value', () => {
    const options = {
      arrayFormat: 'index',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    const key = 'items[0]'
    const value = ''
    const accumulator = {} as unknown as IAccumulator & IAccumulator[]

    parseArrayFormat(key, value, accumulator)

    expect(accumulator).toEqual({ items: { '0': '' } })
  })

  // should handle null value
  it('should handle null value', () => {
    const options = {
      arrayFormat: 'index',
      arrayFormatSeparator: ',',
      parseNumbers: false,
      parseBooleans: false,
      decode: true,
    }

    const parseArrayFormat = parserForArrayFormat(options)

    const key = 'items[0]'
    const value: any = null
    const accumulator = {} as unknown as IAccumulator & IAccumulator[]

    parseArrayFormat(key, value, accumulator)

    expect(accumulator).toEqual({ items: { '0': null } })
  })
})
describe('validateArrayFormatSeparator', () => {
  // Should not throw an error when passed a single character string
  it('should not throw an error when passed a single character string', () => {
    expect(() => {
      validateArrayFormatSeparator('a')
    }).not.toThrow()
  })

  // Should not throw an error when passed a single character string of any character
  it('should not throw an error when passed a single character string of any character', () => {
    expect(() => {
      validateArrayFormatSeparator('!')
    }).not.toThrow()
  })

  // Should not return anything when passed a valid single character string
  it('should not return anything when passed a valid single character string', () => {
    const result = validateArrayFormatSeparator(',')
    expect(result).toBeUndefined()
  })

  // Should throw a TypeError when passed a value that is not a string
  it('should throw a TypeError when passed a value that is not a string', () => {
    expect(() => {
      validateArrayFormatSeparator(123)
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed an empty string
  it('should throw a TypeError when passed an empty string', () => {
    expect(() => {
      validateArrayFormatSeparator('')
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed a string with more than one character
  it('should throw a TypeError when passed a string with more than one character', () => {
    expect(() => {
      validateArrayFormatSeparator('abc')
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed a number
  it('should throw a TypeError when passed a number', () => {
    expect(() => {
      validateArrayFormatSeparator(123)
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed a boolean
  it('should throw a TypeError when passed a boolean', () => {
    expect(() => {
      validateArrayFormatSeparator(true as unknown as string)
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed an object
  it('should throw a TypeError when passed an object', () => {
    expect(() => {
      validateArrayFormatSeparator({} as unknown as string)
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed an array
  it('should throw a TypeError when passed an array', () => {
    expect(() => {
      validateArrayFormatSeparator([])
    }).toThrow(TypeError)
  })

  // Should throw a TypeError when passed null
  it('should throw a TypeError when passed null', () => {
    expect(() => {
      validateArrayFormatSeparator(null as unknown as string)
    }).toThrow(TypeError)
  })
})
