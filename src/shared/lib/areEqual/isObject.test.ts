import { describe, expect, it } from 'vitest'

import { AnyObject } from './areObjectsEqual'
import { isObject } from './isObject'

describe('isObject', () => {
  // Returns true for a non-null object
  it('should return true when object is not null', () => {
    const object = { name: 'John' }
    const result = isObject(object)
    expect(result).toBe(true)
  })

  // Returns true for an object of type 'object'
  it("should return true when object is of type 'object'", () => {
    const object = {}
    const result = isObject(object)
    expect(result).toBe(true)
  })

  // Returns false for a null value
  it('should return false when object is null', () => {
    const object: any = null
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for a boolean value
  it('should return false when object is a boolean value', () => {
    const object = true as unknown as AnyObject
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for a number value
  it('should return false when object is a number value', () => {
    const object = 123 as unknown as AnyObject
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for a string value
  it('should return false when object is a string value', () => {
    const object = 'hello' as unknown as AnyObject
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for an array value
  it('should return false when object is an array value', () => {
    const object = [1, 2, 3]
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for a function value
  it('should return false when object is a function value', () => {
    const object = () => {}
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for a symbol value
  it('should return false when object is a symbol value', () => {
    const object = Symbol('test') as unknown as AnyObject
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns false for an object created with Object.create(null)
  it('should return false when object is created with Object.create(null)', () => {
    const object = Object.create(null)
    const result = isObject(object)
    expect(result).toBe(false)
  })

  // Returns true for an object created with {}
  it('should return true when object is created with {}', () => {
    const object = {}
    const result = isObject(object)
    expect(result).toBe(true)
  })

  // Returns true for an object created with new Object()
  it('should return true when object is created with new Object()', () => {
    const object = new Object()
    const result = isObject(object)
    expect(result).toBe(true)
  })
})
