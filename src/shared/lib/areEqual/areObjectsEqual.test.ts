import { describe, expect, it } from 'vitest'

import { AnyObject, areObjectsEqual, isDeepEqual } from './areObjectsEqual'

describe('areObjectsEqual', () => {
  // The function returns true if two objects are equal in value and type.
  it('should return true when two objects are equal in value and type', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { a: 1, b: 2 }
    expect(areObjectsEqual(obj1, obj2)).toBe(true)
  })

  // The function returns true if two objects are equal in value and type, even if the properties are in different order.
  it('should return true when two objects are equal in value and type, even if properties are in different order', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { b: 2, a: 1 }
    expect(areObjectsEqual(obj1, obj2)).toBe(true)
  })

  // The function returns true if two empty objects are compared.
  it('should return true when two empty objects are compared', () => {
    const obj1 = {}
    const obj2 = {}
    expect(areObjectsEqual(obj1, obj2)).toBe(true)
  })

  // The function returns false if two objects have different values for the same property.
  it('should return false when two objects have different values for the same property', () => {
    const obj1 = { a: 1 }
    const obj2 = { a: 2 }
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns false if two objects have different properties.
  it('should return false when two objects have different properties', () => {
    const obj1 = { a: 1 }
    const obj2 = { b: 2 }
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns true if two objects have the same properties and values, even if they are nested.
  it('should return true when two objects have the same properties and values, even if they are nested', () => {
    const obj1 = { a: { b: 1 } }
    const obj2 = { a: { b: 1 } }
    expect(areObjectsEqual(obj1, obj2)).toBe(true)
  })

  // The function returns false if one of the objects is null or undefined.
  it('should return false when one of the objects is null', () => {
    const obj1 = { a: 1 }
    const obj2: any = null
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns false if one of the objects is a primitive type.
  it('should return false when one of the objects is a primitive type', () => {
    const obj1 = { a: 1 }
    const obj2 = 1 as unknown as AnyObject
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns false if one of the objects is a function.
  it('should return false when one of the objects is a function', () => {
    const obj1 = { a: 1 }
    const obj2 = () => {}
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns false if one of the objects is a circular reference.
  it('should return false when one of the objects is a circular reference', () => {
    const obj1: any = { a: 1 }
    const obj2 = { b: obj1 }
    obj1.b = obj2
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns false if one of the objects has a property with value NaN.
  it('should return false when one of the objects has a property with value NaN', () => {
    const obj1 = { a: 1 }
    const obj2 = { a: NaN }
    expect(areObjectsEqual(obj1, obj2)).toBe(false)
  })

  // The function returns true if two objects have the same properties and values, even if they are in different cases.
  it('should return true when two objects have the same properties and values, even if they are in different cases', () => {
    const obj1 = { a: 1 }
    const obj2 = { A: 1 }
    expect(areObjectsEqual(obj1, obj2)).toBe(true)
  })
})

describe('isDeepEqual', () => {
  // Returns true if both objects are null
  it('should return true when both objects are null', () => {
    const obj1: any = null
    const obj2: any = null
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns true if both objects are of primitive types and have the same value
  it('should return true when both objects are of primitive types and have the same value', () => {
    const obj1 = 5 as unknown as AnyObject
    const obj2 = 5 as unknown as AnyObject
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns true if both objects have the same set of keys and values
  it('should return true when both objects have the same set of keys and values', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { a: 1, b: 2 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns true if both objects have the same set of keys and values, even if the keys are in different cases
  it('should return true when both objects have the same set of keys and values, even if the keys are in different cases', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { A: 1, B: 2 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns true if both objects have the same set of keys and values, even if the values are in different orders
  it('should return true when both objects have the same set of keys and values, even if the values are in different orders', () => {
    const obj1 = { a: 1, b: 2 }
    const obj2 = { b: 2, a: 1 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns true if both objects have the same set of keys and values, even if the values are nested objects
  it('should return true when both objects have the same set of keys and values, even if the values are nested objects', () => {
    const obj1 = { a: { b: 1 }, c: { d: 2 } }
    const obj2 = { a: { b: 1 }, c: { d: 2 } }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(true)
  })

  // Returns false if only one object is null
  it('should return false when only one object is null', () => {
    const obj1: any = null
    const obj2 = { a: 1 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })

  // Returns false if both objects are of different types
  it('should return false when both objects are of different types', () => {
    const obj1 = { a: 1 }
    const obj2 = 'string' as unknown as AnyObject
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })

  // Returns false if both objects have different sets of keys
  it('should return false when both objects have different sets of keys', () => {
    const obj1 = { a: 1 }
    const obj2 = { b: 2 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })

  // Returns false if both objects have the same set of keys but different values
  it('should return false when both objects have the same set of keys but different values', () => {
    const obj1 = { a: 1 }
    const obj2 = { a: 2 }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })

  // Returns false if both objects have the same set of keys and values, but the values are nested objects with different values
  it('should return false when both objects have the same set of keys and values, but the values are nested objects with different values', () => {
    const obj1 = { a: { b: 1 } }
    const obj2 = { a: { b: 2 } }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })

  // Returns false if both objects have the same set of keys and values, but the values are arrays with different values
  it('should return false when both objects have the same set of keys and values, but the values are arrays with different values', () => {
    const obj1 = { a: [1, 2] }
    const obj2 = { a: [1, 3] }
    const result = isDeepEqual(obj1, obj2)
    expect(result).toBe(false)
  })
})
