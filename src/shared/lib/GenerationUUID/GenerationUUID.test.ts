import { describe, expect, it, vi } from 'vitest'

import { generateUUID, isUUID } from './GenerationUUID'

describe('generateUUID', () => {
  // Generates a UUID with correct format
  it('should generate a UUID with correct format', () => {
    const uuid = generateUUID()
    const regex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    expect(regex.test(uuid)).toBe(true)
  })

  // Generates a unique UUID each time it is called
  it('should generate a unique UUID each time it is called', () => {
    const uuid1 = generateUUID()
    const uuid2 = generateUUID()
    expect(uuid1).not.toBe(uuid2)
  })

  // Uses only hexadecimal characters in the UUID
  it('should use only hexadecimal characters in the UUID', () => {
    const uuid = generateUUID()
    const regex = /^[0-9a-f-]+$/i
    expect(regex.test(uuid)).toBe(true)
  })

  // Uses the digits 4 and 8 in the UUID
  it('should use the digits 4 and variant bits in the UUID', () => {
    const uuid = generateUUID()
    expect(uuid.charAt(14)).toBe('4')
    expect(['8', '9', 'a', 'b']).toContain(uuid.charAt(19))
  })

  // Handles generating UUIDs in quick succession
  it('should handle generating UUIDs in quick succession', () => {
    const uuid1 = generateUUID()
    const uuid2 = generateUUID()
    const uuid3 = generateUUID()
    expect(uuid1).not.toBe(uuid2)
    expect(uuid1).not.toBe(uuid3)
    expect(uuid2).not.toBe(uuid3)
  })

  // Handles generating UUIDs after a long period of time
  it('should handle generating UUIDs after a long period of time', () => {
    vi.useFakeTimers()
    const uuid1 = generateUUID()
    vi.advanceTimersByTime(1000)
    const uuid2 = generateUUID()
    expect(uuid1).not.toBe(uuid2)
    vi.useRealTimers()
  })

  // Handles generating UUIDs when timestamp is 0
  it('should handle generating UUIDs when timestamp is 0', () => {
    const originalDateNow = Date.now
    Date.now = vi.fn(() => 0)
    const uuid = generateUUID()
    expect(uuid).toContain('4')
    // UUID v4 doesn't guarantee the presence of '8', it only guarantees
    // that the 13th character is '4' and the 17th is one of '8', '9', 'a', or 'b'
    expect(['8', '9', 'a', 'b']).toContain(uuid.charAt(19))
    Date.now = originalDateNow
  })

  // Generates UUIDs with a uniform distribution of characters
  it('should generate UUIDs with a uniform distribution of characters', () => {
    const uuid = generateUUID()
    const charCounts: any = {}
    for (let i = 0; i < uuid.length; i++) {
      const char = uuid[i]
      if (charCounts[char]) {
        charCounts[char]++
      } else {
        charCounts[char] = 1
      }
    }
    const charDistribution: number[] = Object.values(charCounts)
    const meanCount = charDistribution.reduce((acc, val) => acc + val, 0) / charDistribution.length
    const variance =
      charDistribution.reduce((acc, val) => acc + Math.pow(val - meanCount, 2), 0) / charDistribution.length
    const stdDeviation = Math.sqrt(variance)

    // Проверяем, что стандартное отклонение не превышает 2.0
    expect(stdDeviation).toBeLessThanOrEqual(2.0)
  })

  // Generates UUIDs with a length of 36 characters
  it('should generate UUIDs with a length of 36 characters', () => {
    const uuid = generateUUID()
    expect(uuid.length).toBe(36)
  })

  // Generates UUIDs that are not predictable
  it('should generate UUIDs that are not predictable', () => {
    const uuid1 = generateUUID()
    const uuid2 = generateUUID()
    expect(uuid1).not.toBe(uuid2)
  })
})

describe('isUUID', () => {
  // Returns true for a valid UUID string
  it('should return true when given a valid UUID string', () => {
    const uuid = generateUUID()
    const result = isUUID(uuid)
    expect(result).toBe(true)
  })

  // Returns false for an invalid UUID string
  it('should return false when given an invalid UUID string', () => {
    const uuid = 'invalid-uuid'
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns false for an empty string
  it('should return false when given an empty string', () => {
    const uuid = ''
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns false for a string with more than 36 characters
  it('should return false when given a string with more than 36 characters', () => {
    const uuid = '1234************78901234************7'
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns false for a string with less than 36 characters
  it('should return false when given a string with less than 36 characters', () => {
    const uuid = '1234************78************12345'
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns false for a string with invalid characters
  it('should return false when given a string with invalid characters', () => {
    const uuid = '1234************78************12345!'
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns false for a string with uppercase letters
  it('should return false when given a string with uppercase letters', () => {
    const uuid = '12345678-90AB-CDEF-1234-************'
    const result = isUUID(uuid)
    expect(result).toBe(false)
  })

  // Returns true for a valid UUID string with lowercase letters
  it('should return true when given a valid UUID string with lowercase letters', () => {
    const uuid = '12345678-90ab-cdef-1234-************'
    const result = isUUID(uuid)
    expect(result).toBe(true)
  })

  // Returns true for a valid UUID string with numbers
  it('should return true when given a valid UUID string with numbers', () => {
    const uuid = '12345678-90ab-cdef-5678-************'
    const result = isUUID(uuid)
    expect(result).toBe(true)
  })

  // Returns true for a valid UUID string with hyphens
  it('should return true when given a valid UUID string with hyphens', () => {
    const uuid = '12345678-90ab-cdef-1234-************'
    const result = isUUID(uuid)
    expect(result).toBe(true)
  })

  // Returns true for a valid UUID string with only zeros
  it('should return true when given a valid UUID string with only zeros', () => {
    const uuid = '00000000-0000-0000-0000-000000000000'
    const result = isUUID(uuid)
    expect(result).toBe(true)
  })
})
