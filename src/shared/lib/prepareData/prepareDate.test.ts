import { describe, expect, it } from 'vitest'

import { prepareDate } from './prepareDate'

describe('prepareDate', () => {
  // 1. Корректное форматирование стандартного ввода "YYYY-MM-DD"
  it('should correctly format a date string from "YYYY-MM-DD" to "DD.MM.YYYY"', () => {
    expect(prepareDate('2022-12-31')).toBe('31.12.2022')
    expect(prepareDate('2023-01-01')).toBe('01.01.2023')
    expect(prepareDate('2023-02-28')).toBe('28.02.2023')
  })

  // 2. Обработка строк, имитирующих даты (включая "невалидные" с точки зрения календаря)
  it('should correctly reformat strings that look like dates, including potential leap days or invalid calendar dates', () => {
    // Функция не валидирует, является ли день високосным, просто переставляет части
    expect(prepareDate('2020-02-29')).toBe('29.02.2020') // Високосный
    expect(prepareDate('2021-02-29')).toBe('29.02.2021') // Не високосный, но строка будет обработана
    expect(prepareDate('2100-02-29')).toBe('29.02.2100') // Не високосный, но строка будет обработана
  })

  it('should handle dates before the year 1000 (as strings)', () => {
    expect(prepareDate('0999-12-31')).toBe('31.12.0999')
    expect(prepareDate('0001-01-01')).toBe('01.01.0001')
    expect(prepareDate('0000-12-31')).toBe('31.12.0000') // Если год представлен как "0000"
  })

  it("should reformat strings with 'invalid' month or day numbers as per its logic", () => {
    // Функция не валидирует значения месяца или дня
    expect(prepareDate('2022-13-31')).toBe('31.13.2022') // "Невалидный" месяц
    expect(prepareDate('2022-12-32')).toBe('32.12.2022') // "Невалидный" день
    expect(prepareDate('2022-00-00')).toBe('00.00.2022') // "Нулевые" значения
  })

  // 3. Поведение с некорректными или неожиданными строковыми форматами
  it('should handle strings that are not in "YYYY-MM-DD" format but use "-" as a separator', () => {
    // Если формат "DD-MM-YYYY", результат будет "YYYY.MM.DD"
    expect(prepareDate('31-12-2022')).toBe('2022.12.31')
  })

  it('should handle strings with parts containing non-numeric characters', () => {
    expect(prepareDate('YEAR-MO-DY')).toBe('DY.MO.YEAR')
    expect(prepareDate('2022-MM-DDTHH:MM')).toBe('DDTHH:MM.MM.2022')
  })

  it("should handle strings with insufficient parts after splitting by '-'", () => {
    expect(prepareDate('2022-12')).toBe('undefined.12.2022') // day is undefined
    expect(prepareDate('2022')).toBe('undefined.undefined.2022') // month and day are undefined
  })

  it('should handle empty string input', () => {
    // '' split by '-' -> [''], so year='', month=undefined, day=undefined
    expect(prepareDate('')).toBe('undefined.undefined.')
  })

  it('should handle strings with only hyphens', () => {
    // Input: '-', split: ['', ''] => year='', month='', day=undefined
    expect(prepareDate('-')).toBe('undefined..')
    // Input: '--', split: ['', '', ''] => year='', month='', day=''
    expect(prepareDate('--')).toBe('..')
    // Input: '---', split: ['', '', '', ''] => year='', month='', day=''
    expect(prepareDate('---')).toBe('..')
  })

  it('should handle strings with different separators (other than "-") by not splitting them as expected', () => {
    // split('-') на такой строке вернет массив с одним элементом (вся строка)
    expect(prepareDate('2022/12/31')).toBe('undefined.undefined.2022/12/31')
    expect(prepareDate('31.12.2022')).toBe('undefined.undefined.31.12.2022')
    expect(prepareDate('NoSeparators')).toBe('undefined.undefined.NoSeparators')
  })

  it('should handle strings with leading/trailing spaces around parts if the split occurs', () => {
    // Пробелы вокруг дефисов или внутри частей сохранятся
    expect(prepareDate(' 2022 - 12 - 31 ')).toBe(' 31 . 12 . 2022 ')
    // Если пробелы только по краям всей строки
    expect(prepareDate('2022-12-31 ')).toBe('31 .12.2022') // day = "31 "
    expect(prepareDate(' 2022-12-31')).toBe('31.12. 2022') // year = " 2022"
  })

  // 4. Обработка ошибок при нестроковом вводе
  it('should throw a TypeError if the input is not a string', () => {
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate(123)).toThrowError(TypeError)
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate(true)).toThrowError(TypeError)
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate(null)).toThrowError(TypeError)
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate(undefined)).toThrowError(TypeError)
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate({})).toThrowError(TypeError)
    // @ts-expect-error Testing invalid input type
    expect(() => prepareDate([])).toThrowError(TypeError)
  })
})
