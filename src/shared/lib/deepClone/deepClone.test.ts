import { describe, expect, it } from 'vitest'

import { deepCloneArray, type IRecursiveArray } from './deepClone' // Убедитесь, что путь к файлу верный

describe('deepCloneArray', () => {
  // 1. Базовое клонирование
  it('should return a deep copy of a simple array of numbers', () => {
    const inputArray = [1, 2, 3]
    const result = deepCloneArray(inputArray)
    expect(result).toEqual(inputArray) // Равенство по значению
    expect(result).not.toBe(inputArray) // Неравенство по ссылке
  })

  it('should return a deep copy of a simple array of strings', () => {
    const inputArray = ['a', 'b', 'c']
    const result = deepCloneArray(inputArray)
    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
  })

  it('should work with empty arrays', () => {
    const inputArray: IRecursiveArray = []
    const result = deepCloneArray(inputArray)
    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
  })

  // 2. Глубокое клонирование вложенных массивов
  it('should work with arrays of mixed cloneable types (numbers, strings, nested arrays)', () => {
    const inputArray: IRecursiveArray = ['a', 1, [2, 'b'], [[3]]]
    const result = deepCloneArray(inputArray)
    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
    // Вложенные массивы также должны быть новыми ссылками
    expect(result[2]).not.toBe(inputArray[2])
    expect((result[2] as any[])[0]).toBe(2)
    expect((result[3] as any[])[0]).not.toBe((inputArray[3] as any[])[0])
    expect(((result[3] as any[])[0] as any[])[0]).toBe(3)
  })

  // 3. Обработка элементов, не являющихся массивами, числами или строками
  it('should copy objects within the array by reference', () => {
    const obj = { name: 'John' }
    const inputArray: IRecursiveArray = [obj, { age: 30 }]
    const result = deepCloneArray(inputArray)

    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
    expect(result[0]).toBe(inputArray[0]) // Объект копируется по ссылке
    expect(result[1]).toBe(inputArray[1]) // Объект копируется по ссылке
  })

  it('should copy booleans, null, and undefined within the array as they are', () => {
    // Тип IRecursiveArray может быть слишком строгим здесь, если мы хотим тестировать это напрямую.
    // Однако isCloneable передаст их в ветку else, добавляя их напрямую.
    const inputArray: any[] = [true, false, null, undefined]
    const result = deepCloneArray(inputArray as IRecursiveArray)
    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
  })

  it('should copy functions within the array by reference', () => {
    const myFunc = () => console.log('test')
    const inputArray: any[] = [myFunc]
    const result = deepCloneArray(inputArray as IRecursiveArray)

    expect(result.length).toBe(1)
    expect(result[0]).toBe(myFunc) // Функция копируется по ссылке
    expect(result).not.toBe(inputArray)
  })

  it('should copy Map instances within the array by reference', () => {
    const myMap = new Map([['key', 'value']])
    const inputArray: any[] = [myMap]
    const result = deepCloneArray(inputArray as IRecursiveArray)

    expect(result.length).toBe(1)
    expect(result[0]).toBe(myMap) // Map копируется по ссылке
    expect(result).not.toBe(inputArray)
  })

  it('should copy Set instances within the array by reference', () => {
    const mySet = new Set([1, 2])
    const inputArray: any[] = [mySet]
    const result = deepCloneArray(inputArray as IRecursiveArray)

    expect(result.length).toBe(1)
    expect(result[0]).toBe(mySet) // Set копируется по ссылке
    expect(result).not.toBe(inputArray)
  })

  it('should copy Symbol primitives within the array by reference/value', () => {
    const mySymbol = Symbol('test')
    const inputArray: any[] = [mySymbol]
    const result = deepCloneArray(inputArray as IRecursiveArray)

    expect(result.length).toBe(1)
    expect(result[0]).toBe(mySymbol) // Symbol копируется по ссылке/значению
    expect(result).not.toBe(inputArray)
  })

  // 4. Циклические ссылки
  it('should correctly clone arrays with circular references', () => {
    const inputArray: IRecursiveArray = [1]
    inputArray.push(inputArray) // inputArray = [1, inputArray]
    const result = deepCloneArray(inputArray)

    expect(result).not.toBe(inputArray)
    expect(result.length).toBe(2)
    expect(result[0]).toBe(1)
    expect(result[1]).toBe(result) // Циклическая ссылка должна сохраняться в клоне
    expect(result[1]).not.toBe(inputArray) // но не указывать на исходный массив
  })

  it('should handle more complex circular references in nested arrays', () => {
    const subArray: IRecursiveArray = [2]
    const inputArray: IRecursiveArray = [1, subArray]
    // @ts-ignore // Разрешаем добавление inputArray в subArray для теста
    subArray.push(inputArray) // subArray = [2, inputArray]

    const result = deepCloneArray(inputArray)

    expect(result).not.toBe(inputArray)
    expect(result.length).toBe(2)
    expect(result[0]).toBe(1)

    const clonedSubArray = result[1] as IRecursiveArray
    expect(clonedSubArray).not.toBe(subArray)
    expect(clonedSubArray.length).toBe(2)
    expect(clonedSubArray[0]).toBe(2)
    expect(clonedSubArray[1]).toBe(result) // Циклическая ссылка на новый корень
  })

  // 5. Объекты с неперечислимыми свойствами (объекты копируются поверхностно)
  it('should copy objects with non-enumerable properties by reference', () => {
    const obj = { name: 'John' }
    Object.defineProperty(obj, 'age', {
      value: 30,
      enumerable: false,
    })
    const inputArray: IRecursiveArray = [obj]
    const result = deepCloneArray(inputArray)

    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
    expect(result[0]).toBe(inputArray[0]) // Сам объект копируется по ссылке
    expect(Object.getOwnPropertyDescriptor(result[0] as object, 'age')?.value).toBe(30)
    expect(Object.getOwnPropertyDescriptor(result[0] as object, 'age')?.enumerable).toBe(false)
  })

  // 6. Обработка ошибок для неклонируемого ввода (верхний уровень)
  it('should throw an error if the top-level input is an object', () => {
    const input: any = { a: 1 }
    expect(() => deepCloneArray(input as IRecursiveArray)).toThrowError('Input is not cloneable')
  })

  it('should throw an error if the top-level input is a boolean', () => {
    const input: any = true
    expect(() => deepCloneArray(input as IRecursiveArray)).toThrowError('Input is not cloneable')
  })

  // 7. Клонирование не-массивов, которые "клонируемы" (числа, строки)
  it('should correctly "clone" a top-level number', () => {
    const inputNumber = 123
    // Это немного неправильное использование функции, ориентированной на массивы,
    // но isCloneable это разрешает, и последняя ветка else возвращает его.
    const result = deepCloneArray(inputNumber as any) // Приведение к any для обхода ограничения типа массива
    expect(result).toBe(123)
  })

  it('should correctly "clone" a top-level string', () => {
    const inputString = 'hello'
    const result = deepCloneArray(inputString as any) // Приведение к any
    expect(result).toBe('hello')
  })

  // 8. Предел глубины
  it('should throw an error if the input array is too deeply nested (exceeding default depthLimit)', () => {
    const inputArray: IRecursiveArray = []
    let currentArray: any = inputArray
    // По умолчанию depthLimit = 1000. Нам нужна вложенность 1000 уровней,
    // чтобы 1001-й вызов deepCloneArray имел depthLimit <= 0.
    for (let i = 0; i < 1000; i++) {
      currentArray.push([])
      currentArray = currentArray[0]
    }
    expect(() => deepCloneArray(inputArray)).toThrowError('Input array is too deeply nested')
  })

  it('should not throw an error if the input array is deeply nested but within depthLimit', () => {
    const inputArray: IRecursiveArray = []
    let currentArray: any = inputArray
    // depthLimit = 1000. Сделаем 999 уровней вложенности.
    // Вызов для самого внутреннего массива [] будет с depthLimit = 1000 - 999 = 1.
    for (let i = 0; i < 999; i++) {
      currentArray.push([])
      currentArray = currentArray[0]
    }
    expect(() => deepCloneArray(inputArray)).not.toThrow()
  })

  it('should respect a custom depthLimit', () => {
    const inputArray: IRecursiveArray = [[[]]] // Глубина 2 (A_0=[A_1=[A_2=[]]])
    // Вызов для A_2 будет с depthLimit = customDepthLimit - 2.
    // Если customDepthLimit = 2, то depthLimit = 0 -> throw.
    expect(() => deepCloneArray(inputArray, 2)).toThrowError('Input array is too deeply nested')
    // Если customDepthLimit = 3, то depthLimit = 1 -> no throw.
    expect(() => deepCloneArray(inputArray, 3)).not.toThrow()
  })

  // 9. Объекты со свойствами Symbol (объекты копируются поверхностно)
  it('should copy objects with Symbol properties by reference', () => {
    const symbol = Symbol('test')
    const obj = { [symbol]: 'value', regular: 'prop' }
    const inputArray: IRecursiveArray = [obj]
    const result = deepCloneArray(inputArray)

    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
    expect(result[0]).toBe(inputArray[0]) // Сам объект копируется по ссылке
    expect((result[0] as any)[symbol]).toBe('value')
  })

  // 10. Дополнительные тесты
  it('should push non-array, non-number, non-string elements as they are (by reference for objects)', () => {
    const date = new Date()
    const regex = /abc/g
    const obj = { a: 1 }
    const func = () => {}
    const inputArray: any[] = [date, regex, obj, true, null, undefined, func]
    const result = deepCloneArray(inputArray as IRecursiveArray)

    expect(result).not.toBe(inputArray)
    expect(result.length).toBe(inputArray.length)
    expect(result[0]).toBe(date)
    expect(result[1]).toBe(regex)
    expect(result[2]).toBe(obj)
    expect(result[3]).toBe(true)
    expect(result[4]).toBe(null)
    expect(result[5]).toBe(undefined)
    expect(result[6]).toBe(func)
  })

  it('should use cache for identical cloneable sub-arrays if they appear multiple times (non-circular)', () => {
    const subArray = [1, 2]
    const inputArray: IRecursiveArray = [subArray, subArray, [3, 4]]
    const result = deepCloneArray(inputArray)

    expect(result).toEqual(inputArray)
    expect(result).not.toBe(inputArray)
    expect(result[0]).not.toBe(subArray) // Первый элемент - клон subArray
    expect(result[1]).not.toBe(subArray) // Второй элемент - тоже клон subArray
    // Важно: оба элемента должны указывать на *один и тот же* клонированный subArray из-за кэширования
    expect(result[0]).toBe(result[1])
    expect(result[2]).not.toBe(result[0]) // Третий элемент - другой клон
    expect((result[2] as any[])[0]).toBe(3)
  })

  it('should correctly handle mixed cloneable and non-cloneable items in the loop', () => {
    const objInArray = { id: 1 }
    const nestedArray = [10, 20]
    const inputArray: IRecursiveArray = ['string1', objInArray, nestedArray, 300]
    const result = deepCloneArray(inputArray)

    expect(result).not.toBe(inputArray)
    expect(result.length).toBe(4)

    expect(result[0]).toBe('string1') // Строка "клонируется" (копируется по значению)
    expect(result[1]).toBe(objInArray) // Объект добавляется по ссылке
    expect(result[2]).toEqual(nestedArray) // Вложенный массив глубоко клонируется
    expect(result[2]).not.toBe(nestedArray)
    expect((result[2] as number[])[0]).toBe(10)
    expect(result[3]).toBe(300) // Число "клонируется" (копируется по значению)
  })
})
