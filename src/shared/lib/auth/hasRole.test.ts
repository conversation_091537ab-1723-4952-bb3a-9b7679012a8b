import { ROLES } from 'entities/shared/roles.entities'
import { IUserRole } from 'src/entities/store/auth.entities'
import { describe, expect, it } from 'vitest'

import { hasRole } from './hasRole'

describe('hasRole function', () => {
  const sampleUserRoles: IUserRole[] = [
    { role: ROLES.ADMIN, description: 'Administrator', mandatory: true },
    {
      role: ROLES.TECHNOLOGIST,
      description: 'Technologist',
      mandatory: false,
    },
    {
      role: ROLES.TECH_ADMIN_NSI,
      description: 'Technical NSI Admin',
      mandatory: false,
    },
  ]

  it('should return true if the user has the specified role (exact match from ROLES enum)', () => {
    expect(hasRole(sampleUserRoles, ROLES.ADMIN)).toBe(true)
    expect(hasRole(sampleUserRoles, ROLES.TECHNOLOGIST)).toBe(true)
  })

  it('should return true if the user has the specified role (case-insensitive match for input role string)', () => {
    expect(hasRole(sampleUserRoles, 'admin')).toBe(true)
    expect(hasRole(sampleUserRoles, 'AdMiN')).toBe(true)
    expect(hasRole(sampleUserRoles, 'TECHNOLOGIST')).toBe(true)
    expect(hasRole(sampleUserRoles, 'technologist')).toBe(true)
    expect(hasRole(sampleUserRoles, 'tech_admin_nsi')).toBe(true)
    expect(hasRole(sampleUserRoles, 'TECH_ADMIN_NSI')).toBe(true)
  })

  it('should return false if the user does not have the specified role', () => {
    expect(hasRole(sampleUserRoles, ROLES.GUEST)).toBe(false) // GUEST не в sampleUserRoles
    expect(hasRole(sampleUserRoles, ROLES.TECH_ADMIN_CM)).toBe(false) // TECH_ADMIN_CM не в sampleUserRoles
    expect(hasRole(sampleUserRoles, 'nonexistentrole')).toBe(false)
  })

  it('should return false for an empty roles array', () => {
    const emptyRoles: IUserRole[] = []
    expect(hasRole(emptyRoles, ROLES.ADMIN)).toBe(false)
    expect(hasRole(emptyRoles, 'guest')).toBe(false)
  })

  it('should correctly handle roles with underscores from ROLES enum', () => {
    expect(hasRole(sampleUserRoles, ROLES.TECH_ADMIN_NSI)).toBe(true)
    // Проверка с строковым значением, соответствующим enum
    expect(hasRole(sampleUserRoles, 'TECH_ADMIN_NSI')).toBe(true)
    // Проверка с строковым значением в нижнем регистре
    expect(hasRole(sampleUserRoles, 'tech_admin_nsi')).toBe(true)
  })

  it('should return false if the searched role is a substring of an existing role but not a full match', () => {
    expect(hasRole(sampleUserRoles, 'Admi')).toBe(false) // Ищем "Admi", есть "ADMIN"
    expect(hasRole(sampleUserRoles, 'Tech')).toBe(false) // Ищем "Tech", есть "TECHNOLOGIST" и "TECH_ADMIN_NSI"
    expect(hasRole(sampleUserRoles, 'TECH_ADMIN_')).toBe(false) // Ищем "TECH_ADMIN_", есть "TECH_ADMIN_NSI"
  })

  it('should find a role even if it is not the first one in the array', () => {
    const rolesOrderTest: IUserRole[] = [
      { role: ROLES.GUEST, description: 'Guest', mandatory: false },
      {
        role: ROLES.TECHNOLOGIST,
        description: 'Technologist',
        mandatory: false,
      },
      { role: ROLES.ADMIN, description: 'Admin', mandatory: true },
    ]
    expect(hasRole(rolesOrderTest, ROLES.ADMIN)).toBe(true)
    expect(hasRole(rolesOrderTest, 'technologist')).toBe(true)
    expect(hasRole(rolesOrderTest, ROLES.GUEST)).toBe(true)
  })

  it('should handle cases where ROLES enum values are already uppercase (as per definition)', () => {
    // Этот тест становится менее критичным, так как значения в ROLES уже в верхнем регистре,
    // но он подтверждает, что toLowerCase() на обеих сторонах работает ожидаемо.
    const rolesAllUpper: IUserRole[] = [
      { role: ROLES.ADMIN, description: 'Admin', mandatory: true },
      {
        role: ROLES.TECH_ADMIN_CM,
        description: 'Tech CM Admin',
        mandatory: false,
      },
    ]
    expect(hasRole(rolesAllUpper, 'admin')).toBe(true)
    expect(hasRole(rolesAllUpper, 'ADMIN')).toBe(true)
    expect(hasRole(rolesAllUpper, 'tech_admin_cm')).toBe(true)
    expect(hasRole(rolesAllUpper, 'TECH_ADMIN_CM')).toBe(true)
  })
})
