import { beforeEach, describe, expect, it, vi } from 'vitest'

import * as localStorageModule from './localStorage'

const { JSDOM } = require('jsdom')

const { getTokens, getTokensAndInformation, setTokens } = localStorageModule

vi.mock('./localStorage', async (importOriginal) => {
  const actual = await importOriginal()

  return Object.assign({}, actual, {
    setTokenReceiptTime: vi.fn(), // Создаем мок-функцию setTokenReceiptTime
  })
})

const jsdom = new JSDOM('<!doctype html><html><body></body></html>', {
  url: 'http://localhost/',
})
const { window } = jsdom
global.window = window
global.document = window.document
// Мокируем метод localStorage.setItem
const mockSetItem = vi.fn()
Object.defineProperty(global, 'localStorage', {
  value: {
    ...global.localStorage,
    setItem: mockSetItem,
  },
})

describe('getTokens', () => {
  beforeEach(() => {
    window.localStorage.clear()
  })

  // Returns an object with 'refreshToken' and 'token' properties when both values are present in localStorage
  it('should return an object with "refreshToken" and "token" properties when both values are present in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', 'refreshTokenValue')
    window.localStorage.setItem('token', 'tokenValue')

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when only 'refreshToken' is present in localStorage
  it('should return an object with "refreshToken" and "token" properties when only "refreshToken" is present in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', 'refreshTokenValue')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: 'refreshTokenValue',
      token: null,
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when only 'token' is present in localStorage
  it('should return an object with "refreshToken" and "token" properties when only "token" is present in localStorage', () => {
    // Arrange
    window.localStorage.removeItem('refreshToken')
    window.localStorage.setItem('token', 'tokenValue')

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: null,
      token: 'tokenValue',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when neither value is present in localStorage
  it('should return an object with "refreshToken" and "token" properties when neither value is present in localStorage', () => {
    // Arrange
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: null,
      token: null,
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when 'refreshToken' and 'token' are empty strings in localStorage
  it('should return an object with "refreshToken" and "token" properties when "refreshToken" and "token" are empty strings in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', '')
    window.localStorage.setItem('token', '')

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: '',
      token: '',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when 'refreshToken' and 'token' are null in localStorage
  it('should return an object with "refreshToken" and "token" properties when "refreshToken" and "token" are null in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', null)
    window.localStorage.setItem('token', null)

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: 'null',
      token: 'null',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when 'refreshToken' and 'token' are undefined in localStorage
  it('should return an object with "refreshToken" and "token" properties when "refreshToken" and "token" are undefined in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', undefined)
    window.localStorage.setItem('token', undefined)

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: 'undefined',
      token: 'undefined',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when 'refreshToken' and 'token' are not strings in localStorage
  it('should return an object with "refreshToken" and "token" properties when "refreshToken" and "token" are not strings in localStorage', () => {
    // Arrange
    window.localStorage.setItem('refreshToken', 123)
    window.localStorage.setItem('token', true)

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: '123',
      token: 'true',
    })
  })

  // Returns an object with 'refreshToken' and 'token' properties when 'refreshToken' and 'token' are not present in localStorage and localStorage.getItem throws an error
  it('should return an object with "refreshToken" and "token" properties when "refreshToken" and "token" are not present in localStorage and localStorage.getItem throws an error', () => {
    // Arrange
    const originalGetItem = window.localStorage.getItem
    window.localStorage.getItem = vi.fn(() => {
      throw new Error('getItem error')
    })

    // Act
    const result = getTokens()

    // Assert
    expect(result).toEqual({
      refreshToken: null,
      token: null,
    })

    // Restore the original implementation
    window.localStorage.getItem = originalGetItem
  })
})

describe('getTokensAndInformation', () => {
  beforeEach(() => {
    window.localStorage.clear()
  })

  // Returns an object with refreshToken, token and userDetail properties when localStorage has all three items.
  it('should return an object with refreshToken, token and userDetail properties when localStorage has all three items', () => {
    // Arrange
    const expected = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: { name: 'John Doe', age: 25 },
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.setItem('userDetail', JSON.stringify(expected.userDetail))

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with refreshToken and token properties when localStorage has only refreshToken and token items.
  it('should return an object with refreshToken and token properties when localStorage has only refreshToken and token items', () => {
    // Arrange
    const expected: any = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: null, // Изменение ожидаемого значения на null
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.removeItem('userDetail') // Удаление userDetail из localStorage

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is null.
  it('should return an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is null', () => {
    // Arrange
    const expected: any = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: null, // Изменение ожидаемого значения на null
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.setItem('userDetail', JSON.stringify(null)) // Сохранение как строку JSON

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is undefined.
  it('should return an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is undefined', () => {
    // Arrange
    const expected: any = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: null,
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.setItem('userDetail', 'undefined') // Set as string "undefined"

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is an empty object.
  it('should return an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is an empty object', () => {
    // Arrange
    const expected = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: {},
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.setItem('userDetail', JSON.stringify({}))

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is a non-empty object.
  it('should return an object with refreshToken and token properties when localStorage has only refreshToken and token items, and userDetail item is a non-empty object', () => {
    // Arrange
    const expected = {
      refreshToken: 'refreshTokenValue',
      token: 'tokenValue',
      userDetail: { name: 'John Doe', age: 25 },
    }
    window.localStorage.setItem('refreshToken', expected.refreshToken)
    window.localStorage.setItem('token', expected.token)
    window.localStorage.setItem('userDetail', JSON.stringify(expected.userDetail))

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage is empty.
  it('should return an object with empty refreshToken and token properties when localStorage is empty', () => {
    // Arrange
    const expected: any = {
      refreshToken: null,
      token: null,
      userDetail: null,
    }
    window.localStorage.clear()

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage has only userDetail item.
  it('should return an object with empty refreshToken and token properties when localStorage has only userDetail item', () => {
    // Arrange
    const expected: any = {
      refreshToken: null,
      token: null,
      userDetail: { name: 'John Doe', age: 25 },
    }
    window.localStorage.setItem('userDetail', JSON.stringify({ name: 'John Doe', age: 25 }))
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is null.
  it('should return an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is null', () => {
    // Arrange
    const expected: any = {
      refreshToken: null,
      token: null,
      userDetail: null,
    }
    window.localStorage.setItem('userDetail', null)
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is undefined.
  it('should return an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is undefined', () => {
    // Arrange
    const expected: any = {
      refreshToken: null,
      token: null,
      userDetail: null, // null, так как userDetail отсутствует
    }
    window.localStorage.setItem('userDetail', undefined) // Установить userDetail в undefined
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is an empty object.
  it('should return an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is an empty object', () => {
    // Arrange
    const expected: any = {
      refreshToken: null, // Изменение на null
      token: null, // Изменение на null
      userDetail: {},
    }
    window.localStorage.setItem('userDetail', JSON.stringify({}))
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })

  // Returns an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is a non-empty object.
  it('should return an object with empty refreshToken and token properties when localStorage has only userDetail item, and userDetail item is a non-empty object', () => {
    // Arrange
    const expected: any = {
      refreshToken: null,
      token: null,
      userDetail: { name: 'John Doe', age: 25 },
    }
    window.localStorage.setItem('userDetail', JSON.stringify(expected.userDetail))
    window.localStorage.removeItem('refreshToken')
    window.localStorage.removeItem('token')

    // Act
    const result = getTokensAndInformation()

    // Assert
    expect(result).toEqual(expected)
  })
})

describe('setTokens', () => {
  beforeEach(() => {
    mockSetItem.mockClear()
  })
  // Sets the refreshToken and token in localStorage
  it('should set the refreshToken and token in localStorage', () => {
    const refreshToken = 'refreshToken'
    const token = 'token'
    setTokens({ refreshToken, token })
    expect(localStorage.setItem).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorage.setItem).toHaveBeenNthCalledWith(2, 'token', token)
  })

  // refreshToken and token are empty strings
  it('should set the refreshToken and token as empty strings', () => {
    const refreshToken = ''
    const token = ''
    setTokens({ refreshToken, token })
    expect(localStorage.setItem).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorage.setItem).toHaveBeenNthCalledWith(2, 'token', token)
  })

  // refreshToken and token are null
  it('should set the refreshToken and token as null', () => {
    const refreshToken: any = null
    const token: any = null
    setTokens({ refreshToken, token })
    expect(localStorage.setItem).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorage.setItem).toHaveBeenNthCalledWith(2, 'token', token)
  })

  // refreshToken and token are undefined
  it('should set the refreshToken and token as undefined', () => {
    const refreshToken: any = undefined
    const token: any = undefined
    setTokens({ refreshToken, token })
    expect(localStorage.setItem).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorage.setItem).toHaveBeenNthCalledWith(2, 'token', token)
  })

  // refreshToken and token are not strings
  it('should set the refreshToken and token as stringified values', () => {
    // Шпионирование функции localStorage.setItem
    const localStorageSetItemSpy = vi.spyOn(localStorage, 'setItem')

    const refreshToken: any = { value: 'refreshToken' }
    const token: any = { value: 'token' }

    // Вызов функции setTokens
    setTokens({ refreshToken, token })

    // Проверка, что localStorage.setItem вызывается дважды с правильными аргументами
    expect(localStorageSetItemSpy).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorageSetItemSpy).toHaveBeenNthCalledWith(2, 'token', token)

    // Восстановление оригинальной функции localStorage.setItem
    localStorageSetItemSpy.mockRestore()
  })

  // localStorage is full and cannot store refreshToken and token
  it('should throw an error if localStorage.setItem fails', () => {
    const refreshToken = 'refreshToken'
    const token = 'token'
    mockSetItem.mockImplementationOnce(() => {
      throw new Error()
    })
    expect(() => {
      setTokens({ refreshToken, token })
    }).toThrow()
  })

  // Calls localStorage.setItem with correct parameters
  it('should call localStorage.setItem with correct parameters', () => {
    const refreshToken = 'refreshToken'
    const token = 'token'
    setTokens({ refreshToken, token })
    expect(localStorage.setItem).toHaveBeenNthCalledWith(1, 'refreshToken', refreshToken)
    expect(localStorage.setItem).toHaveBeenNthCalledWith(2, 'token', token)
  })

  // Returns undefined
  it('should return undefined', () => {
    const refreshToken = 'refreshToken'
    const token = 'token'
    const result = setTokens({ refreshToken, token })
    expect(result).toBeUndefined()
  })

  // Throws an error if localStorage.setItem fails
  it('should throw an error if localStorage.setItem fails', () => {
    const refreshToken = 'refreshToken'
    const token = 'token'
    mockSetItem.mockImplementationOnce(() => {
      throw new Error()
    })
    expect(() => {
      setTokens({ refreshToken, token })
    }).toThrow()
  })
})
