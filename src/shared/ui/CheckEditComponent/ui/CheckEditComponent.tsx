import { PromptProviderContext } from 'app/providers/PromptNoSaveProvider'
import { ReactNode, useContext, useEffect } from 'react'

interface ICheckEditComponent {
  children: ReactNode
  isEdit: boolean
}

export const CheckEditComponent = ({ children, isEdit = false }: ICheckEditComponent) => {
  const { togglePromptNoSave } = useContext(PromptProviderContext)

  useEffect(() => {
    togglePromptNoSave(isEdit)
    localStorage.setItem('editMode', JSON.stringify(isEdit))

    return () => {
      localStorage.removeItem('editMode')
    }
  }, [isEdit])

  return <>{children}</>
}
