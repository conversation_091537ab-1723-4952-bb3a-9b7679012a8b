import { Divider } from '@mui/material'
import Button from '@mui/material/Button'
import Card from '@mui/material/Card'
import CardHeader from '@mui/material/CardHeader'
import Checkbox from '@mui/material/Checkbox'
import Grid from '@mui/material/Grid'
import List from '@mui/material/List'
import ListItem from '@mui/material/ListItem'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import * as React from 'react'
import { useEffect, useState } from 'react'

function not(a: IDataSourceItem[], b: IDataSourceItem[]) {
  return a.filter((value) => !b.includes(value))
}

function intersection(a: IDataSourceItem[], b: IDataSourceItem[]) {
  return a.filter((value) => b.includes(value))
}

function union(a: IDataSourceItem[], b: IDataSourceItem[]) {
  const newB = b.filter((item) => !item.disabled)

  return [...a, ...not(newB, a)]
}

interface IDataSourceItem {
  key: string
  title: string
  disabled?: boolean
}
interface ITransferProps {
  choicesTitle: string
  chosenTitle: string
  dataSource: IDataSourceItem[]
  targetKeys: string[]
  onChange: (nextTargetKeys: string[]) => void
}

export const Transfer = (props: ITransferProps) => {
  const { choicesTitle, chosenTitle, targetKeys, dataSource, onChange } = props
  const [checked, setChecked] = useState<IDataSourceItem[]>([])
  const [left, setLeft] = useState<IDataSourceItem[]>([])
  const [right, setRight] = useState<IDataSourceItem[]>([])

  useEffect(() => {
    const left = []
    const right = []

    for (const item of dataSource) {
      if (targetKeys.includes(item.key)) right.push(item)
      else left.push(item)
    }

    setLeft(left)
    setRight(right)
  }, [dataSource, targetKeys])

  const leftChecked = intersection(checked, left)
  const rightChecked = intersection(checked, right)

  const handleToggle = (value: IDataSourceItem) => () => {
    const currentIndex = checked.indexOf(value)
    const newChecked = [...checked]

    if (currentIndex === -1) {
      newChecked.push(value)
    } else {
      newChecked.splice(currentIndex, 1)
    }

    setChecked(newChecked)
  }

  const numberOfChecked = (items: IDataSourceItem[]) => intersection(checked, items).length

  const handleToggleAll = (items: IDataSourceItem[]) => () => {
    if (numberOfChecked(items) === items.filter((item) => !item.disabled).length) {
      setChecked(not(checked, items))
    } else {
      setChecked(union(checked, items))
    }
  }

  const handleCheckedRight = () => {
    setRight(right.concat(leftChecked))
    setLeft(not(left, leftChecked))
    setChecked(not(checked, leftChecked))
    onChange(right.concat(leftChecked).map((item) => item.key))
  }

  const handleCheckedLeft = () => {
    setLeft(left.concat(rightChecked))
    setRight(not(right, rightChecked))
    setChecked(not(checked, rightChecked))
    onChange(not(right, rightChecked).map((item) => item.key))
  }

  const customList = (title: React.ReactNode, items: IDataSourceItem[]) => (
    <Card>
      <CardHeader
        sx={{ px: 2, py: 1 }}
        avatar={
          <Checkbox
            onClick={handleToggleAll(items)}
            checked={
              numberOfChecked(items) === items.filter((item) => !item.disabled).length &&
              items.filter((item) => !item.disabled).length !== 0
            }
            indeterminate={
              numberOfChecked(items) !== items.filter((item) => !item.disabled).length && numberOfChecked(items) !== 0
            }
            disabled={items.filter((item) => !item.disabled).length === 0}
          />
        }
        title={title}
        subheader={`${numberOfChecked(items)}/${items.length} выбрано`}
      />
      <Divider />
      <List
        sx={{
          height: 300,
          bgcolor: 'background.paper',
          overflow: 'auto',
        }}
        dense
        component='div'
        role='list'
      >
        {items.map((value: IDataSourceItem) => {
          const labelId = `transfer-list-all-item-${value.key}-label`

          return (
            <ListItem key={value.key} role='listitem' button onClick={handleToggle(value)} disabled={value.disabled}>
              <ListItemIcon>
                <Checkbox
                  checked={checked.includes(value)}
                  tabIndex={-1}
                  disableRipple
                  inputProps={{
                    'aria-labelledby': labelId,
                  }}
                />
              </ListItemIcon>
              <ListItemText id={labelId}>{value.title}</ListItemText>
            </ListItem>
          )
        })}
      </List>
    </Card>
  )

  return (
    <Grid container spacing={2} justifyContent='center' alignItems='center'>
      <Grid xs={5} item>
        {customList(choicesTitle, left)}
      </Grid>
      <Grid item>
        <Grid xs={2} container direction='column' alignItems='center'>
          <Button
            sx={{ my: 0.5 }}
            variant='outlined'
            size='small'
            onClick={handleCheckedRight}
            disabled={leftChecked.length === 0}
            aria-label='move selected right'
          >
            &gt;
          </Button>
          <Button
            sx={{ my: 0.5 }}
            variant='outlined'
            size='small'
            onClick={handleCheckedLeft}
            disabled={rightChecked.length === 0}
            aria-label='move selected left'
          >
            &lt;
          </Button>
        </Grid>
      </Grid>
      <Grid xs={5} item>
        {customList(chosenTitle, right)}
      </Grid>
    </Grid>
  )
}
