import { classNames } from 'shared/lib/classNames/classNames'
import { Icon } from 'shared/ui/Icon'

import cls from './EmptyBlockForComponent.module.scss'

interface EmptyBlockForComponentProps {
  className?: string
}

export const EmptyBlockForComponent = (props: EmptyBlockForComponentProps) => {
  const { className } = props

  return (
    <div className={classNames(cls.EmptyBlockForComponent, {}, className ? [className] : [])}>
      <div className={classNames(cls.NoDataContainer, {}, [])}>
        <Icon name='noData' width={184} height={140} />
      </div>
      <p>Нет данных</p>
    </div>
  )
}
