.DropZoneContainer {
  width: 100%;
}
.MessageSection {
  position: relative;
}
.MessageInput {
}
.MessageLabel {
  position: relative;
  text-align: center;
}
.MessageContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  //border-width: 1px;
  //border-radius: 5px;
  ////border-color: ${(props) => getColor(props)};
  //border-color: var(--text-gray);
  //border-style: dashed;
  background-color: var(--background-color-secondary);
  border: 1px dashed var(--primary-color);
  color: var(--primary-color);
  outline: none;
  //transition: border 0.24s ease-in-out;
  transition: all 0.24s ease-in-out;
  margin: 5px 0;
  cursor: pointer;
}
.isDragAccept {
  border: 1px dashed var(--primary-color);
  color: var(--primary-color);
}
.isDragReject {
  border: 1px dashed var(--primary-color);
  color: var(--primary-color);
}
.isDragActive {
  border: 1px dashed var(--primary-color);
  color: var(--primary-color);
}
.FileSection {
  padding: 20px;
  margin: 5px 0;
}
.FileContainer {
  display: flex;
}
.FileList {
  padding: 10px 20px;
  width: 100%;
}
.FileCaption {
}
.File {
}
.FileActions {
  align-items: center;
  display: flex;
  cursor: pointer;
  opacity: 0.3;
  transition: all 0.2s;
  &:hover {
    opacity: 1;
  }
}
