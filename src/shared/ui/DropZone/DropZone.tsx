import CloseIcon from '@mui/icons-material/Close'
import React, { FC, useCallback } from 'react'
import { Accept, useDropzone } from 'react-dropzone'
import { classNames } from 'shared/lib/classNames/classNames'

import cls from './DropZone.module.scss'

interface FilesProps extends File {
  path?: string
}

export interface DropZoneProps {
  label?: string | JSX.Element
  accept?: Accept
  files?: FilesProps[] | []
  setFiles: React.Dispatch<React.SetStateAction<FilesProps[]>>
  disabled?: boolean
  errors?: [string]
  name?: string
  onBlur?: () => void
  readOnly?: boolean
  dataTest?: string
}

export const DropZone: FC<DropZoneProps> = (props) => {
  const {
    label = 'Перетащите или выберите файл',
    accept = { 'text/plain': ['.xls', '.xlsx'] },
    files = [],
    setFiles,
    disabled = false,
    errors = [],
    name,
    onBlur,
  } = props
  const onDrop = useCallback((acceptedFiles: File[]) => setFiles([...files, ...acceptedFiles]), [files, setFiles])
  const removeAll = () => setFiles([])
  const { getRootProps, getInputProps, isDragActive, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    accept,
    disabled,
  })
  const hasFiles = !!files.length
  const hasErrors = !!errors.length

  return (
    <section className={cls.DropZoneContainer}>
      {!hasFiles && (
        <div className={cls.MessageSection}>
          <div
            className={classNames(
              cls.MessageContainer,
              {
                [cls.isDragActive]: isDragActive,
                [cls.isDragAccept]: isDragAccept,
                [cls.isDragReject]: isDragReject,
              },
              [],
            )}
            // disabled={disabled}
            {...getRootProps({
              isDragActive,
              isDragAccept,
              isDragReject,
              errors: hasErrors,
              disabled,
            })}
            onBlur={onBlur}
          >
            <input className={cls.MessageInput} {...getInputProps()} name={name} readOnly />
            <p className={cls.MessageLabel}>{label}</p>
          </div>
          {/*{hasErrors && <ErrorTooltip errors={errors} />}*/}
        </div>
      )}

      {hasFiles && (
        <div className={cls.FileSection}>
          <div className={cls.FileContainer}>
            <div className={cls.FileList}>
              {files.map((file) => {
                return (
                  <div className={cls.File} key={file.path}>
                    {file.path} - {file.size} bytes
                  </div>
                )
              })}
            </div>
            {!disabled && (
              <div className={cls.FileActions} onClick={removeAll}>
                <CloseIcon />
              </div>
            )}
          </div>
        </div>
      )}
    </section>
  )
}
