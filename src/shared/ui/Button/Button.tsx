import { Tooltip } from '@mui/material'
import ButtonMui, { type ButtonProps } from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import { type FC, ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Icon } from 'shared/ui/Icon'
import { type IconNameProps } from 'shared/ui/Icon/Icon.type'

import cls from './Button.module.scss'

interface IButtonProps extends ButtonProps {
  className?: string
  message?: string | ReactNode | null
  loading?: boolean
  disabled?: boolean
  icon?: null | IconNameProps
}

const InternalButton: FC<IButtonProps> = (props) => {
  const {
    variant = 'contained',
    className,
    children,
    loading = false,
    icon = null,
    disabled = false,
    ...otherProps
  } = props

  return (
    <ButtonMui
      variant={variant}
      disabled={disabled || loading}
      {...otherProps}
      className={classNames(
        cls.button,
        {
          [cls.contained]: variant === 'contained',
          [cls.text]: variant === 'text',
          [cls.outlined]: variant === 'outlined',
        },
        className ? [className] : [],
      )}
    >
      {loading && <CircularProgress size={20} />}
      {!loading && icon && <Icon name={icon} width={20} height={20} />}
      {children}
    </ButtonMui>
  )
}

export const Button: FC<IButtonProps> = (props) => {
  const {
    variant = 'contained',
    className,
    children,
    loading = false,
    icon = null,
    disabled = false,
    message = null,
    ...otherProps
  } = props

  if (message) {
    return (
      <Tooltip title={message}>
        <div>
          <InternalButton
            icon={icon}
            variant={variant}
            loading={loading}
            disabled={disabled}
            className={className}
            {...otherProps}
          >
            {children}
          </InternalButton>
        </div>
      </Tooltip>
    )
  }

  return (
    <InternalButton
      icon={icon}
      variant={variant}
      loading={loading}
      disabled={disabled}
      className={className}
      {...otherProps}
    >
      {children}
    </InternalButton>
  )
}
