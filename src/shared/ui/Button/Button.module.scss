.button {
  display: inline-flex !important;
  padding: 4px 10px !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 10px !important;
  font-weight: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-transform: initial !important;
  font-family: var(--font-family-main) !important;

  &:disabled {
    color: rgb(0 0 0 / 26%) !important;
    box-shadow: none;
    background-color: rgb(0 0 0 / 12%) !important;
    border: solid 1px transparent !important;
  }
}

//background-color: rgba(255, 255, 255, 0.12) !important;
//color: rgba(255, 255, 255, 0.3) !important;
//cursor: no-drop !important;
//

.contained {
  background-color: var(--primary-color) !important;
}

.text {
  color: var(--primary-color) !important;
}

.outlined {
  color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
}
