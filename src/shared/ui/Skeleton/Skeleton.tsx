import { classNames } from 'shared/lib/classNames/classNames.ts'

import cls from './Skeleton.module.scss'

interface ISkeleton {
  type?: 'Circle' | 'Square'
  width?: number
  height?: number
}

export const Skeleton = (props: ISkeleton) => {
  const { type = 'Circle', width, height } = props
  if (type === 'Circle') {
    return (
      <div
        style={{
          width: width ? width : 20,
          height: height ? height : 20,
        }}
        className={classNames(cls.Circle, {}, [cls.Background])}
      ></div>
    )
  }
  if (type === 'Square') {
    return (
      <div
        style={{
          width: width ? width : 100,
          height: height ? height : 20,
        }}
        className={classNames(cls.Square, {}, [cls.Background])}
      ></div>
    )
  }

  return <></>
}
