import { observer } from 'mobx-react'
import { DragEvent, ReactNode, useEffect, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Icon } from 'shared/ui/Icon'
import { IconNameProps } from 'shared/ui/Icon/Icon.type.ts'

import cls from './MenuDragAndDrop.module.scss'

export interface MenuDragAndDropProps {
  className?: string
  items?: CurrentItemProps[]
  disabled?: boolean
  isDragMode?: boolean
  disabledChangePosition?: boolean
  customCell?: (item: CurrentItemProps) => ReactNode
  onChangePosition?: (res: string[]) => void
  select: number | null
  setSelect: (selectedValue: number) => void
  isEdit?: boolean
  isAcceptedColor?: boolean
  typeSort?: 'alphabeat' | 'custom'
  actions?: number[]
  floods?: number[]
  isLastDay?: boolean
  changeActions?: (value: { plantId: number }) => void
  changeFloods?: (value: { plantId: number }) => void
  isViewActions?: boolean
}

export interface CurrentItemProps {
  plantId: number
  value: string | number
  viewOnly: boolean
  mixing: boolean
  accepted: boolean
  label: string
  icon?: IconNameProps
  type: 'GES' | 'GAES'
}

export interface IDragAndDrop {
  draggedFrom: null | number
  draggedTo: null | number
  isDragging: boolean
  originalOrder: CurrentItemProps[]
  updatedOrder: CurrentItemProps[]
}

const initialDnDState = {
  draggedFrom: null,
  draggedTo: null,
  isDragging: false,
  originalOrder: [],
  updatedOrder: [],
}

export const MenuDragAndDrop = observer((props: MenuDragAndDropProps) => {
  const {
    className,
    customCell,
    items,
    onChangePosition,
    disabled = false,
    select,
    setSelect,
    disabledChangePosition,
    isEdit,
    isAcceptedColor = false,
    actions = [],
    floods = [],
    changeActions,
    changeFloods,
    isViewActions = true,
    isLastDay = false,
    isDragMode = false,
  } = props

  const [cardList, setCardList] = useState<CurrentItemProps[]>([])

  useEffect(() => {
    const initCardList = items ? items.map((el, index: number) => ({ ...el, order: index + 1 })) : []
    setCardList(initCardList)
  }, [items])

  const [drag, setDrag] = useState<string | null>(null)

  const canDrag = !!drag && !disabled

  const [dragAndDrop, setDragAndDrop] = useState<IDragAndDrop>(initialDnDState)

  const onDragStart = (event: DragEvent<HTMLDivElement>) => {
    event.stopPropagation()
    const initialPosition = Number(event.currentTarget.dataset.position)
    setDragAndDrop({
      ...dragAndDrop,
      draggedFrom: initialPosition,
      isDragging: true,
      originalOrder: cardList,
    })
    event.dataTransfer.setData('text/html', '')
  }

  const onDragOver = (event: DragEvent<HTMLDivElement>) => {
    event.preventDefault()
    event.stopPropagation()
    let newList: CurrentItemProps[] = dragAndDrop.originalOrder
    const draggedFrom = dragAndDrop.draggedFrom as number
    const draggedTo = Number(event.currentTarget.dataset.position)
    const itemDragged = newList[draggedFrom]
    const remainingItems = newList.filter((_, index) => index !== draggedFrom)
    newList = [...remainingItems.slice(0, draggedTo), itemDragged, ...remainingItems.slice(draggedTo)]

    if (draggedTo !== dragAndDrop.draggedTo) {
      setDragAndDrop({
        ...dragAndDrop,
        updatedOrder: newList,
        draggedTo: draggedTo,
      })
    }
  }

  const onDrop = () => {
    setCardList(dragAndDrop.updatedOrder)
    if (onChangePosition) {
      onChangePosition(dragAndDrop.updatedOrder.map((el) => String(el.value)))
    }
    setDragAndDrop({
      ...dragAndDrop,
      draggedFrom: null,
      draggedTo: null,
      isDragging: false,
    })
  }

  const onDragLeave = () => {
    setDragAndDrop({
      ...dragAndDrop,
      draggedTo: null,
    })
  }

  return (
    <div className={classNames(cls.MenuDragAndDrop, {}, className ? [className] : [])}>
      {cardList.length > 0 ? (
        <>
          {cardList.map((card, index) => {
            const selectAction = actions.some((el) => el === card.plantId)
            const selectFlood = floods.some((el) => el === card.plantId)
            const dropArea = dragAndDrop && dragAndDrop.draggedTo === Number(index)
            const isVault = card.value === 0

            return (
              <div
                key={card.plantId}
                data-position={index}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDrop={onDrop}
                onDragLeave={onDragLeave}
                draggable={canDrag && !isVault}
                className={classNames(
                  cls.Card,
                  {
                    [cls.Draggable]: !isVault,
                    [cls.dropArea]: dropArea,
                    [cls.DisabledChange]: disabledChangePosition || isVault,
                    [cls.Accepted]: card.accepted && isAcceptedColor,
                  },
                  [],
                )}
                id='dragItem'
                onClick={() => {
                  if (isEdit) {
                    const answer = window.confirm(
                      'Изменения сохранены не будут. Вы действительно хотите перейти на другую станцию?',
                    )
                    if (answer) {
                      if (select !== Number(card.value)) {
                        if (!disabledChangePosition) {
                          setSelect(Number(card.value))
                        }
                      }
                    }
                  } else {
                    if (select !== Number(card.value)) {
                      if (!disabledChangePosition) {
                        setSelect(Number(card.value))
                      }
                    }
                  }
                }}
              >
                {isDragMode && (
                  <div
                    id='dragonIcon'
                    className={classNames(cls.DragIcon, { [cls.isDrag]: !disabled && !isVault }, [])}
                    onMouseEnter={() => {
                      if (!isVault) {
                        setDrag(String(card?.value))
                      }
                    }}
                    onMouseLeave={() => setDrag(null)}
                  >
                    {!isVault && <Icon width={18} name='dragAndDrop' />}
                  </div>
                )}
                <div className={classNames(cls.Label, {}, [])}>{customCell ? customCell(card) : card?.label}</div>
                {isViewActions && select === 0 && card.mixing && !card.viewOnly && (
                  <div className={cls.ButtonsActions}>
                    {!isLastDay && (
                      <button
                        type='button'
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          if (!disabledChangePosition && !isLastDay) {
                            changeActions && changeActions(card)
                          }
                        }}
                        className={classNames(
                          cls.OtherButton,
                          {
                            [cls.ActiveAction]: selectAction,
                            [cls.DisabledAction]: disabledChangePosition,
                            [cls.AcceptedAction]: isLastDay,
                          },
                          [cls.Action],
                        )}
                      >
                        V
                      </button>
                    )}
                    {card.plantId !== 0 ? (
                      <button
                        type='button'
                        onClick={(e) => {
                          e.preventDefault()
                          e.stopPropagation()
                          if (!disabledChangePosition && !card.accepted && !isLastDay) {
                            changeFloods && changeFloods(card)
                          }
                        }}
                        className={classNames(
                          cls.OtherButton,
                          {
                            [cls.ActiveFlood]: selectFlood,
                            [cls.DisabledAction]: disabledChangePosition,
                            [cls.AcceptedAction]: card.accepted || isLastDay,
                          },
                          [cls.Flood],
                        )}
                      >
                        П
                      </button>
                    ) : (
                      <div className={cls.OtherButton} />
                    )}
                  </div>
                )}
              </div>
            )
          })}
        </>
      ) : (
        <div className={cls.Empty}>Нет данных</div>
      )}
    </div>
  )
})
