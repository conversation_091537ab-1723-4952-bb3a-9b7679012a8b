.MenuDragAndDrop {
  height: 100%;
  width: 100%;
  overflow: auto;
}

.isDrag {
  cursor: move;
}

.DisabledChange {
  border: solid 1px transparent !important;
  cursor: default;
  border-radius: 0;
}

.Card {
  position: relative;
  border: solid 1px transparent;
  height: 33px;
  margin: 0 0 0.1em 0;
  padding: 0;
  display: flex;
  align-items: center;
  line-height: 1;
  border-radius: 5px;
  &:hover {
    border: solid 1px var(--primary-color) !important;
  }
}

.Draggable {
  cursor: move;

  &:hover {
    .isDrag {
      color: var(--text-gray) !important;
    }
  }
}

.Icon {
  width: 33px;
  height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.Label {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-color);
  width: 100%;
  min-width: 168px;
}

.DragIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 40px;
  color: transparent;
  &:not(.isDrag) {
    height: 100%;
    width: 40px;
  }
}

.Select {
  color: var(--primary-color);
}

.Empty {
  width: 100%;
  height: 100%;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--gray-background);
  border-radius: 10px;
}

.Accepted {
  background-color: var(--bg-color-accept) !important;
}

.OtherButton {
  width: 20px;
  min-width: 20px;
  max-width: 20px;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  border: solid 1px transparent;
  border-radius: 4px;
  transition: all 0.3s;
}
.Action {
  background-color: var(--gray-background);
  color: var(--blue-color);
  border: solid 1px var(--blue-color);
  margin-right: 4px !important;
}
.ActiveAction {
  background-color: var(--blue-color);
  color: white;
}
.Flood {
  background-color: var(--gray-background);
  color: var(--green-color);
  border: solid 1px var(--green-color);
}
.ActiveFlood {
  background-color: var(--green-color);
  color: white;
}

.DisabledAction {
  background-color: var(--gray-background);
  border: solid 1px #000000;
  color: var(--text-gray);
}
.AcceptedAction {
  opacity: 0.6;
  cursor: default;
}

.dropArea {
  border: 1px dashed var(--blue-color);
}

.ButtonsActions {
  margin-right: 4px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

@keyframes fadeIn {
  0% {
    opacity: 0.2;
  }
  100% {
    opacity: 1;
  }
}
