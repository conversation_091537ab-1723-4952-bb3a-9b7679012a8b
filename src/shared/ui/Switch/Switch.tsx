import { FormControlLabel, FormGroup, Switch as SwitchMui, type SwitchProps } from '@mui/material'

import cls from './Switch.module.scss'

interface ISwitchProps extends SwitchProps {
  label?: string
  disabled?: boolean
}

export const Switch = (props: ISwitchProps) => {
  const { label = '', checked, onChange, disabled = false } = props

  return (
    <FormGroup>
      <FormControlLabel
        className={cls.Switch}
        control={<SwitchMui disabled={disabled} checked={checked} onChange={onChange} size='small' />}
        label={label}
      />
    </FormGroup>
  )
}
