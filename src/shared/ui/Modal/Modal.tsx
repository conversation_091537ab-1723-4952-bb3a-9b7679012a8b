import CloseIcon from '@mui/icons-material/Close'
import { Dialog, DialogActions, DialogContent, type DialogProps, DialogTitle, IconButton } from '@mui/material'
import { useTheme } from 'app/providers/ThemeProvider'
import { type ReactNode, useEffect, useRef } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'

import cls from './Modal.module.scss'

export interface IModalProps extends Omit<DialogProps, 'title' | 'open'> {
  open?: boolean
  title?: string | ReactNode
  onClose?: () => void
  className?: string
  actions?: ReactNode
  isVisibleCloseBtn?: boolean
  skipConfirmOnClose?: boolean
}

export const Modal = (props: IModalProps) => {
  const { theme } = useTheme()
  const {
    title = '',
    open = true,
    onClose,
    className,
    actions,
    children,
    isVisibleCloseBtn = true,
    skipConfirmOnClose = false,
    ...otherProps
  } = props

  const descriptionElementRef = useRef<HTMLElement>(null)

  useEffect(() => {
    if (open) {
      const { current: descriptionElement } = descriptionElementRef
      if (descriptionElement !== null) {
        descriptionElement.focus()
      }
    }
  }, [open])

  return (
    <Dialog
      className={classNames(cls.Main, {}, [theme, className ? className : ''])}
      open={open}
      scroll='paper'
      {...otherProps}
    >
      {title && (
        <DialogTitle>
          {title}
          {onClose && isVisibleCloseBtn ? (
            <IconButton
              aria-label='close'
              onClick={() => {
                if (!skipConfirmOnClose) {
                  const isEdit = JSON.parse(localStorage.getItem('editMode') as string) ?? false
                  if (isEdit) {
                    const answer = window.confirm(
                      'Изменения сохранены не будут. Вы действительно хотите закрыть модальное окно?',
                    )
                    if (answer) {
                      onClose()
                    }

                    return
                  }
                }
                onClose()
              }}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
                color: 'var(--primary-color)',
              }}
            >
              <CloseIcon className={cls.IconButton} />
            </IconButton>
          ) : null}
        </DialogTitle>
      )}
      {children && (
        <DialogContent id='DialogContent' dividers ref={descriptionElementRef} tabIndex={-1}>
          {children}
        </DialogContent>
      )}
      {actions && (
        <DialogActions>
          <div className={cls.Actions}>{actions}</div>
        </DialogActions>
      )}
    </Dialog>
  )
}
