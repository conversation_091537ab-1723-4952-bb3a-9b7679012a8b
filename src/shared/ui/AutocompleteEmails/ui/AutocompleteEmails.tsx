import { AutocompleteProps } from '@mui/material/Autocomplete'
import TextField from '@mui/material/TextField'
import { FC, FocusEventHandler, type KeyboardEvent, useEffect, useState } from 'react'
import { Autocomplete } from 'shared/ui/Autocomplete'

export interface AutocompleteEmailsProps
  extends Omit<AutocompleteProps<string, true, false, false>, 'renderInput' | 'options' | 'onError'> {
  onValuesChange: (emails: string[]) => void
  onError?: (error: string | null) => void
  error?: boolean
}

export const AutocompleteEmails: FC<AutocompleteEmailsProps> = (props) => {
  const { onValuesChange, value, onError, error: errorProp, ...autocompleteProps } = props
  const [values, setValues] = useState<string[] | undefined>(value)
  const [error, setError] = useState<boolean | undefined>(false)
  const [inputValue, setInputValue] = useState<string>('')

  useEffect(() => {
    setError((prev) => prev || errorProp)
  }, [errorProp])

  const handleChangeValue = (value: string[]) => {
    setValues(value)
    onValuesChange(value)
  }

  const isValidEmail = (email: string) => {
    // Email validation check
    const emailRegex =
      /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]+)$/
    if (!emailRegex.test(email)) {
      setError(true) // Set error state if the value does not match the email format
      onError && onError('Поле не соответствует шаблону <EMAIL>')

      return false
    }
    onError && onError(null)
    setError(false) // Reset error state when values change

    return true
  }

  const handleKeyDown = (event: KeyboardEvent<HTMLDivElement>, value: string) => {
    if ((event.key === ' ' || event.key === 'Enter') && value && value.trim() !== '') {
      const trimmedValue = value.trim()
      if (!isValidEmail(trimmedValue)) {
        return
      }
      if (values) {
        handleChangeValue([...values, trimmedValue])
      }
      event.preventDefault() // Prevent the space or Enter from being entered into the input

      // Reset input value
      setInputValue('')
    }
  }

  const handleBlur: FocusEventHandler<HTMLInputElement | HTMLTextAreaElement> = (event) => {
    const value = event.target.value.trim()
    if (!!value && isValidEmail(value) && values !== undefined) {
      const updatedValues = [...values, value]
      setValues(updatedValues)
      onValuesChange(updatedValues)

      // Reset input value
      setInputValue('')
    }
  }

  return (
    <Autocomplete
      // freeSolo={false}
      open={false}
      multiple
      value={values}
      options={[]}
      popupIcon={false}
      inputValue={inputValue}
      onChange={(_, value) => handleChangeValue(value)}
      onInputChange={(_, newInputValue) => setInputValue(newInputValue)}
      renderInput={(params) => (
        <TextField
          {...params}
          error={error}
          onKeyDown={(event) => handleKeyDown(event, params.inputProps.value as string)}
          onBlur={handleBlur}
        />
      )}
      {...autocompleteProps}
    />
  )
}
