.button {
  display: inline-flex !important;
  padding: 4px 8px !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 10px !important;
  font-weight: normal !important;
  line-height: normal !important;
  letter-spacing: normal !important;
  text-transform: initial !important;
  font-family: var(--font-family-main) !important;

  &:disabled {
    color: rgb(0 0 0 / 26%) !important;
    box-shadow: none;
    background-color: rgb(0 0 0 / 12%) !important;
    border: solid 1px transparent !important;
  }
}

.contained {
  background-color: var(--primary-color) !important;
  color: white !important;
  box-shadow:
    0px 3px 1px -2px rgba(0, 0, 0, 0.2),
    0px 2px 2px 0px rgba(0, 0, 0, 0.14),
    0px 1px 5px 0px rgba(0, 0, 0, 0.12);
  
  &:hover {
    box-shadow:
      0px 2px 4px -1px rgba(0, 0, 0, 0.2),
      0px 4px 5px 0px rgba(0, 0, 0, 0.14),
      0px 1px 10px 0px rgba(0, 0, 0, 0.12);
    }
}

.text {
  color: var(--primary-color) !important;
}

.outlined {
  color: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
}
