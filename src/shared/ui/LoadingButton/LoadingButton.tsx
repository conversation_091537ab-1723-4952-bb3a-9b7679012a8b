import { Tooltip } from '@mui/material'
import Button, { type ButtonProps } from '@mui/material/Button'
import CircularProgress, { type CircularProgressProps } from '@mui/material/CircularProgress'
import { classNames } from 'shared/lib/classNames'

import cls from './LoadingButton.module.scss'

type LoadingButtonProps = {
  loading: boolean
  children: React.ReactNode
  progressProps?: CircularProgressProps
  message?: string | React.ReactNode | null
}

export const LoadingButton = ({
  variant = 'text',
  loading,
  children,
  progressProps,
  startIcon,
  endIcon,
  className,
  message = null,
  ...buttonProps
}: ButtonProps & LoadingButtonProps) => {
  const button = (
    <Button
      {...buttonProps}
      className={classNames(
        cls.button,
        {
          [cls.contained]: variant === 'contained',
          [cls.text]: variant === 'text',
          [cls.outlined]: variant === 'outlined',
        },
        className ? [className] : [],
      )}
      disabled={loading || buttonProps.disabled}
      startIcon={loading && startIcon ? <CircularProgress size={16} {...progressProps} /> : startIcon}
      endIcon={loading && endIcon ? <CircularProgress size={16} {...progressProps} /> : endIcon}
    >
      {loading && !startIcon && !endIcon ? <CircularProgress size={16} {...progressProps} /> : children}
    </Button>
  )

  return message ? (
    <Tooltip title={message}>
      <div>{button}</div>
    </Tooltip>
  ) : (
    button
  )
}
