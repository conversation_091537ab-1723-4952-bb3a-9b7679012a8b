import Highcharts from 'highcharts'
import * as HighchartsStock from 'highcharts/highstock'
import exportingModule from 'highcharts/modules/exporting'
import offlineExporting from 'highcharts/modules/offline-exporting'
import HighchartsReactComponent from 'highcharts-react-official'
import { type FC } from 'react'
import { Loader } from 'shared/ui/Loader'

import cls from './Chart.module.scss'

exportingModule(Highcharts)
offlineExporting(Highcharts)

Highcharts.setOptions({
  lang: {
    weekdays: ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота'],
    loading: 'Загрузка...',
    months: [
      'Январь',
      'Февраль',
      'Март',
      'Апрель',
      'Май',
      'Июнь',
      'Июль',
      'Август',
      'Сентябрь',
      'Октябрь',
      'Ноябрь',
      'Декабрь',
    ],
    shortMonths: ['Янв', 'Фев', 'Март', 'Апр', 'Май', 'Июнь', 'Июль', 'Авг', 'Сен', 'Окт', 'Нояб', 'Дек'],
    downloadPNG: 'Загрузить PNG',
    downloadJPEG: 'Загрузить JPEG',
    downloadPDF: 'Загрузить PDF',
    downloadSVG: 'Загрузить SVG',
    resetZoom: 'Сбросить',
    resetZoomTitle: 'Сбросить',
    viewFullscreen: 'Полноэкранный режим',
    exitFullscreen: 'Выйти из полноэкранного режима',
  },
  exporting: {
    fallbackToExportServer: false,
  },
})

export interface ChartProps {
  options: Highcharts.Options
  isLoading?: boolean
  constructorType?: string
}

export const Chart: FC<ChartProps> = (props) => {
  const { options, isLoading, constructorType } = props

  return (
    <div className={cls.ChartContainer}>
      {isLoading ? (
        <div className={cls.Loader}>
          <Loader />
        </div>
      ) : (
        <>
          {options?.series && options?.series?.length > 0 ? (
            <HighchartsReactComponent
              containerProps={{
                className: cls.ChartContainer,
              }}
              highcharts={constructorType !== 'stockChart' ? Highcharts : HighchartsStock}
              constructorType={constructorType}
              options={{
                ...options,
                credits: {
                  enabled: false,
                },
              }}
            />
          ) : (
            <div className={cls.Empty}>Нет данных</div>
          )}
        </>
      )}
    </div>
  )
}
