import { type ROLES } from 'entities/shared/roles.entities.ts'
import { observer } from 'mobx-react'
import { type FC, ReactNode } from 'react'
import { useStore } from 'stores/useStore'

export interface AccessControlProps {
  rules?: ROLES[]
  children?: ReactNode
}

export const AccessControl: FC<AccessControlProps> = observer((props) => {
  const { authStore } = useStore()
  const { userDetail } = authStore
  const { rules = [], children } = props
  const isAccess = userDetail?.roles
    ? userDetail?.roles
        .map((el) => el.role)
        .some((el: string) => {
          return rules.some((rule) => rule === el)
        })
    : []
  if (isAccess) {
    return <>{children}</>
  } else {
    return <></>
  }
})
