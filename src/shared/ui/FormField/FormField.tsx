import { FC, ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames/classNames.ts'

import cls from './FormField.module.scss'

interface FormFieldProps {
  label: ReactNode
  field: ReactNode
  className?: string
  labelClassName?: string
  fieldClassName?: string
}

export const FormField: FC<FormFieldProps> = (props) => {
  const { label, field, className = '', labelClassName = '', fieldClassName } = props

  return (
    <div className={classNames(cls.row, {}, [className])}>
      <div className={classNames(cls.title, {}, [labelClassName])}>{label}</div>
      <div className={fieldClassName}>{field}</div>
    </div>
  )
}
