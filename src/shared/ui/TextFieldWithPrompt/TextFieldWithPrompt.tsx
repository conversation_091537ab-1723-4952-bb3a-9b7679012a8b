import { Popper } from '@mui/material'
import { ChangeEvent, RefObject, useRef, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { useOnClickOutside } from 'shared/lib/useOnClickOutside'
import { TextField } from 'shared/ui/TextField'

import cls from './TextFieldWithPrompt.module.scss'

interface ITextFieldWithPromptProps {
  value?: string
  items: string[]
  onChange: (_: string) => void
  className?: string
  error?: boolean
  helperText: string
  onBlur?: (_: string) => void
  disabled?: boolean
}

export const TextFieldWithPrompt = (props: ITextFieldWithPromptProps) => {
  const { value = '', items, onChange, onBlur, className, error, helperText, disabled } = props

  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null)
  const [open, setOpen] = useState(false)
  const refBlock = useRef(null)
  const refPopper = useRef(null)
  const refInput = useRef<HTMLInputElement>(null)

  const handleAddPropmt = (prompt: string) => {
    const res = `${value ?? ''}${prompt}`
    onChange(res)
    onBlur && onBlur(res)
    if (refInput.current) {
      refInput.current.focus()
    }
  }

  useOnClickOutside(refBlock, () => {
    setAnchorEl(null)
    setOpen(false)
  }, [refPopper])

  const refBlockWidth = (refBlock as RefObject<HTMLDivElement>)?.current?.clientWidth ?? 0

  return (
    <div ref={refBlock} className={classNames(cls.block, {}, className ? [className] : [])}>
      <TextField
        inputRef={refInput}
        value={value}
        onChange={(e: ChangeEvent<HTMLInputElement>) => onChange(e?.target?.value)}
        onFocus={() => {
          setAnchorEl(refBlock.current)
          setOpen(true)
        }}
        autoComplete='off'
        className={cls.input}
        error={error}
        helperText={helperText}
        onBlur={(e) => onBlur && onBlur(e?.target?.value)}
        disabled={disabled}
      />
      <Popper
        ref={refPopper}
        open={open}
        anchorEl={anchorEl}
        placement='bottom-start'
        className={cls.popper}
        style={{ width: `${refBlockWidth}px` }}
      >
        <ul className={cls.list}>
          {items.map((item) => (
            <div key={`key-${item}`} className={cls.item} onClick={() => handleAddPropmt(item)}>
              {item}
            </div>
          ))}
        </ul>
      </Popper>
    </div>
  )
}
