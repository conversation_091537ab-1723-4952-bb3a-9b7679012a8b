.block {
  //   width: fit-content;
}

.popper {
  z-index: 9000;
  background-color: #fff;
  border-radius: 4px;
  box-shadow:
    0px 5px 5px -3px rgba(0, 0, 0, 0.2),
    0px 8px 10px 1px rgba(0, 0, 0, 0.14),
    0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.list {
  padding-top: 8px;
  padding-bottom: 8px;
}

.item {
  min-height: auto;
  padding: 6px 16px;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  cursor: pointer;

  &:hover {
    background-color: #0263d9;
    color: #fff;
    transition: background-color 0.3s;
  }
}

.input {
  width: 100%;
}
