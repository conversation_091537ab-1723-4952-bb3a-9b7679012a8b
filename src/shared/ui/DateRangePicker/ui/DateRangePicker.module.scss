div.dateRangePicker {
  min-width: unset !important;

  & [class$='MuiInputBase-root-MuiOutlinedInput-root'],
  & .MuiOutlinedInput-input {
    border-radius: 6px;
    font-size: 0.75rem !important;
    font-weight: 600;

    & input {
      padding: 0.25em 1em;
    }
  }

  &Error {
    fieldset {
      border-color: var(--red-color) !important;
    }

    input {
      color: var(--red-color) !important;
    }
  }
}

.RangePickerIcon {
  padding: 0 !important;
}

.ActionButton {
  font-size: 30px;
  width: 26px !important;
  height: 26px !important;
}
