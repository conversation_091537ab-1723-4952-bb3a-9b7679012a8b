import React, { ReactElement, ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'

import s from './SubtitleWithActions.module.scss'

interface Props {
  title?: string | Element | ReactElement
  actions?: ReactNode[] | string[] | undefined
  className?: string
  isActionsVisible?: boolean
}

export const SubtitleWithActions = (props: Props) => {
  const { title, actions, isActionsVisible, className } = props

  return (
    <div className={classNames(s.container, {}, className ? [className] : [])}>
      {title && <>{typeof title === 'string' ? <h2>{title}</h2> : title}</>}
      {isActionsVisible && actions ? (
        <div className={s.actions}>
          {actions.map((action, index) => (
            <React.Fragment key={`action-headers-${index}`}>{action}</React.Fragment>
          ))}
        </div>
      ) : (
        <></>
      )}
    </div>
  )
}
