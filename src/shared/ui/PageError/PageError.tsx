import { useTheme } from 'app/providers/ThemeProvider'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'

import cls from './PageError.module.scss'

interface PageErrorProps {
  className?: string
}

export const PageError = (props: PageErrorProps) => {
  const { className } = props
  const { theme } = useTheme()

  const reloadPage = () => {
    location.reload()
  }

  return (
    <div className={classNames(cls.PageError, {}, [className ? className : '', theme])}>
      <h2>Неизвестная ошибка</h2>
      <Button onClick={reloadPage}>Обновить страницу</Button>
    </div>
  )
}
