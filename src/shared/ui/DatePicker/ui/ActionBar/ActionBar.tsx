import EventBusyIcon from '@mui/icons-material/EventBusy'
import TodayIcon from '@mui/icons-material/Today'
import { DialogActions, IconButton, Tooltip } from '@mui/material'
import { PickersActionBarProps } from '@mui/x-date-pickers/PickersActionBar'

import cls from './ActionBar.module.scss'

interface CustomActionBarProps extends PickersActionBarProps {
  disabled?: boolean
  setNullValue: (value: boolean) => void
}

export const ActionBar = (props: CustomActionBarProps) => {
  const { onSetToday, actions, className, disabled, setNullValue } = props
  if (actions === null || actions?.length === 0) {
    return null
  }

  const calendarActions = (
    <>
      <Tooltip title='Сегодня'>
        <IconButton
          key='today'
          data-mui-test='today-action-button'
          disabled={disabled}
          onClick={() => {
            onSetToday()
            setNullValue(false)
          }}
        >
          <TodayIcon className={cls.actionButton} color='primary' />
        </IconButton>
      </Tooltip>
      <Tooltip title='Сбросить'>
        <IconButton
          key='clear'
          data-mui-test='clear-action-button'
          disabled={disabled}
          onClick={() => {
            setNullValue(true)
          }}
        >
          <EventBusyIcon className={cls.actionButton} color='error' />
        </IconButton>
      </Tooltip>
    </>
  )

  return <DialogActions className={className}>{calendarActions}</DialogActions>
}
