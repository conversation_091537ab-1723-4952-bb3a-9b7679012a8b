import { classNames } from 'shared/lib/classNames'
import { Icon } from 'shared/ui'

import cls from './ButtonArrow.module.scss'

interface ButtonArrowProps {
  onClick?: () => void
  disabled?: boolean
  iconSize?: number
  iconName:
    | 'backDoubleArrowForDatePicker'
    | 'backArrowForDatePicker'
    | 'arrowForDatePicker'
    | 'doubleArrowForDatePicker'
}

export const ButtonArrow = (props: ButtonArrowProps) => {
  const { onClick, disabled, iconSize = 20, iconName } = props

  return (
    <button
      type='button'
      className={classNames(cls.buttonArrow, { [cls.disabledButton]: disabled }, [])}
      onClick={onClick}
    >
      <Icon name={iconName} width={iconSize} height={iconSize} />
    </button>
  )
}
