import { TextField, TextFieldProps } from '@mui/material'
import { format as formatDate, isValid } from 'date-fns'
import { ru } from 'date-fns/locale'
import { classNames } from 'shared/lib/classNames'
import { convertDate } from 'shared/lib/dateFormates'

import cls from './TextFieldWithDayWeek.module.scss'

export type TextFieldWithDayWeekProps = TextFieldProps & { isDayOfWeek?: boolean }

export const TextFieldWithDayWeek = (props: TextFieldWithDayWeekProps) => {
  const { isDayOfWeek, disabled, value, ...rest } = props

  const date = convertDate(value)
  const isValidDate = date && isValid(new Date(date))

  return (
    <div className={cls.dayOfWeekContainer}>
      {isDayOfWeek && isValidDate && (
        <div className={classNames(cls.dayOfWeek, { [cls.disabled]: disabled }, [])}>
          <div className={cls.dayOfWeekText}>
            {`${formatDate(new Date(date), 'EEEEEE', { locale: ru }).toUpperCase()}`}
          </div>
        </div>
      )}
      <TextField {...rest} value={value} disabled={disabled} />
    </div>
  )
}
