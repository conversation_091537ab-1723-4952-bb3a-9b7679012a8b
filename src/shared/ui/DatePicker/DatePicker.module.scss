.dateContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}

.legacyDateContainer {
  justify-content: unset;
}

.datePicker {
  & .MuiOutlinedInput-root {
    padding-right: 11px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
  }
  input {
    padding: 0.25em 1em;
    border-radius: 0;
  }

  [class*='MuiInputAdornment-outlined'] {
    margin: 0;

    button {
      padding: 0;
      margin: 0;
    }
  }

  [class*='MuiInputBase-root MuiOutlinedInput-root'] {
    padding-right: 8px;
    height: 100%;
  }

  &Error {
    fieldset {
      border-color: var(--red-color) !important;
    }

    input {
      color: var(--red-color) !important;
    }
  }
}

.isDayOfWeek {
  input {
    padding: 0 0 0 26px;
    border-radius: 0;
    line-height: 16px !important;
    height: 16px !important;
  }
}

.errorMessage {
  color: var(--red-color);
  font-weight: 400;
  font-size: 0.75rem;
  line-height: 1.66;
  letter-spacing: 0.03333em;
}

.dialogActions {
  display: flex;
  flex-direction: column;
  align-items: start;
}
