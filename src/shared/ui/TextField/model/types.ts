import { type TextFieldProps as TextFieldPropsMui } from '@mui/material/TextField'
import { type IconNameProps } from 'shared/ui/Icon/Icon.type'

export interface NumberOption {
  positive?: boolean
  lengthBeforeComma?: number
  lengthAfterComma?: number
  isInteger?: boolean
  max?: number
  min?: number
  allowExponent?: boolean
}

export interface TextFieldProps extends Omit<TextFieldPropsMui, 'className'> {
  className?: string
  icon?: IconNameProps | 'user' | 'password' | null
  value?: string | number
  maxLength?: number | null
  placeholder?: string
  maxNumber?: number
  maxLengthBeforeComma?: number
  toFixed?: number
  positionIcon?: 'start' | 'end'
  positiveNumber?: boolean
  disabled?: boolean
  numberOption?: NumberOption
}
