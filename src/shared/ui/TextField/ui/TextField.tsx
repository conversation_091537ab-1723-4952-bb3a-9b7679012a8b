import { AccountCircle, LockOutlined, Visibility, VisibilityOff } from '@mui/icons-material'
import {
  FilledInput,
  FormControl,
  FormHelperText,
  IconButton,
  Input,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  TextField as TextFieldMui,
} from '@mui/material'
import React, { type ChangeEvent, useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { generateUUID } from 'shared/lib/GenerationUUID'
import { Icon } from 'shared/ui/Icon'

import { getRegExpForNumber, parseNumberWithComma } from '../lib'
import { type TextFieldProps } from '../model/types'
import cls from './TextField.module.scss'

const emptyNumberOption: TextFieldProps['numberOption'] = {}

export const TextField = (props: TextFieldProps) => {
  const {
    label,
    className,
    variant = 'standard',
    icon = null,
    type,
    value,
    onChange,
    onBlur,
    id,
    error,
    helperText,
    inputProps,
    placeholder,
    maxLength = null,
    maxNumber,
    maxLengthBeforeComma,
    toFixed,
    positiveNumber,
    numberOption = emptyNumberOption,
    positionIcon = 'start',
    disabled = false,
  } = props

  const getIcon = () => {
    if (icon === 'user') {
      return <AccountCircle />
    }
    if (icon === 'password') {
      return <LockOutlined />
    }
    if (icon) {
      return <Icon width={20} name={icon} />
    }
  }

  const [showPassword, setShowPassword] = useState(false)

  const handleClickShowPassword = () => {
    setShowPassword((show) => !show)
  }

  const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
  }

  const uniqKey = id || generateUUID()

  const changeText = (e: ChangeEvent<HTMLInputElement>) => {
    if (maxLength) {
      if (e.target.value.length <= maxLength) {
        onChange?.(e)
      }
    } else {
      onChange?.(e)
    }
  }

  const changeNumber = (e: ChangeEvent<HTMLInputElement>) => {
    // Пустое поле или только числа, минус в самом начале и одна точка/запятая
    if (
      !getRegExpForNumber({
        positive: numberOption?.positive ?? positiveNumber,
        lengthBeforeComma: numberOption?.lengthBeforeComma ?? maxLengthBeforeComma,
        lengthAfterComma: numberOption?.lengthAfterComma ?? toFixed,
        isInteger: numberOption?.isInteger,
        allowExponent: numberOption?.allowExponent,
      }).test(e.target.value)
    ) {
      return
    }
    const parsedValue = parseNumberWithComma(e.target.value)

    if (
      ((!maxNumber || +parsedValue <= maxNumber) &&
        (!numberOption?.max || +parsedValue <= numberOption?.max) &&
        (!numberOption?.min || +parsedValue >= numberOption?.min)) ||
      (!(positiveNumber || numberOption?.positive) && parsedValue === '-')
    ) {
      if (e.target.value.includes(',')) {
        const caretPos = e.target.value.indexOf(',') + 1
        e.target.value = parsedValue
        e.target.setSelectionRange(caretPos, caretPos)
      } else {
        e.target.value = parsedValue
      }
      onChange?.(e)
    }
  }

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()
    e.stopPropagation()

    switch (type) {
      case 'number':
        changeNumber(e)
        break

      default:
        changeText(e)
        break
    }
  }

  if (icon || type === 'password') {
    const inputComponents = {
      standard: Input,
      filled: FilledInput,
      outlined: OutlinedInput,
    }

    const InputComponent = inputComponents[variant] || Input

    const inputAdornments = {
      start: icon && positionIcon === 'start' ? <InputAdornment position='start'>{getIcon()}</InputAdornment> : null,
      end:
        type === 'password' ? (
          <InputAdornment position='end'>
            <IconButton onClick={handleClickShowPassword} onMouseDown={handleMouseDownPassword}>
              {showPassword ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </InputAdornment>
        ) : positionIcon === 'end' ? (
          <InputAdornment position='end'>{getIcon()}</InputAdornment>
        ) : null,
    }

    const inputPropsWithAdornments = {
      sx: {
        ...(disabled
          ? {}
          : {
              '&:hover::before': {
                borderBottom: '1px solid var(--primary-color) !important',
              },
            }),
      },
      value,
      id: uniqKey,
      placeholder,
      type: showPassword ? 'text' : type,
      startAdornment: inputAdornments.start,
      endAdornment: inputAdornments.end,
      disabled,
      onChange: handleChange,
      onBlur,
      error,
    }

    return (
      <FormControl variant={variant} className={classNames(cls.TextField, {}, className ? [className] : [])}>
        <InputLabel htmlFor={uniqKey}>{label}</InputLabel>
        <InputComponent {...inputPropsWithAdornments} />
        <FormHelperText error={error}>{helperText}</FormHelperText>
      </FormControl>
    )
  } else {
    // Вытаскиваем пропы, которые не должны передавать в DOM, во избежание ошибок в логах.
    // Неиспользуемые переменные для пропов именуем с _, чтобы не ругался eslint
    const {
      numberOption: _numberOption,
      maxLength: _maxLength,
      maxNumber: _maxNumber,
      maxLengthBeforeComma: _maxLengthBeforeComma,
      toFixed: _toFixed,
      positiveNumber: _positiveNumber,
      positionIcon: _positionIcon,
      ...otherProps
    } = props

    return (
      <TextFieldMui
        {...otherProps}
        type={type === 'number' ? 'text' : type}
        placeholder={placeholder}
        value={value}
        onChange={handleChange}
        className={classNames(cls.CustomInput, {}, className ? [className] : [])}
        error={error}
        helperText={helperText}
        inputProps={inputProps}
        InputLabelProps={{ shrink: true }}
      />
    )
  }
}
