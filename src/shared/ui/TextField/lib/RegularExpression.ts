import { type NumberOption } from '../model/types'

export const getRegExpForNumber = (options: NumberOption): RegExp => {
  const { positive, lengthBeforeComma, lengthAfterComma, isInteger, allowExponent } = options

  const positiveRegex = positive ? '' : '-?'
  const beforeCommaRegex = `\\d${lengthBeforeComma ? `{0,${lengthBeforeComma}}` : '*'}`
  const afterCommaRegex =
    isInteger || lengthAfterComma === 0 ? '' : `([,.]\\d${lengthAfterComma ? `{0,${lengthAfterComma}}` : '*'})?`
  // Ограничение экспоненты двумя цифрами для предотвращения переполнения (Infinity) или потери значимости (0)
  const exponentRegex = allowExponent ? `([eE][-+]?\\d{0,2})?` : ''

  const fullRegex = `^(${positiveRegex}${beforeCommaRegex}${afterCommaRegex}${exponentRegex})?$`

  return new RegExp(fullRegex)
}

export const getRegExtForUrl = (): RegExp => {
  return /(^$)|(^h$)|(^ht$)|(^htt$)|(^https?$)|(^https?:$)|(^https?:\/$)|(^https?:\/\/)/
}

export const positiveNumbersRegexPattern = /^[1-9][0-9]*$/

export const urlRegexPattern = /^(https?):\/\/[-a-zA-Z0-9+.]*(:[0-9]{1,5})?/

export const emailRegexPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
