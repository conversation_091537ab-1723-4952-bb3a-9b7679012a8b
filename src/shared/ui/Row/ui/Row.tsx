import { memo, type ReactNode } from 'react'
import { classNames } from 'shared/lib/classNames'

import cls from './Row.module.scss'

interface RowProps {
  label?: ReactNode | null
  children?: ReactNode
  rowClassName?: string
  labelClassName?: string
  contentClassName?: string
}

export const Row = memo(({ label, children, rowClassName, labelClassName, contentClassName }: RowProps) => {
  return (
    <div className={classNames(cls.row, {}, [rowClassName ?? ''])}>
      {label !== undefined && <div className={classNames(cls.labelRow, {}, [labelClassName ?? ''])}>{label}</div>}
      <div className={contentClassName ?? ''}>{children}</div>
    </div>
  )
})
