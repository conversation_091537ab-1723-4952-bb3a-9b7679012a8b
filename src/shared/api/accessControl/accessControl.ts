import { axiosInstance as api } from 'shared/lib/axios'
import {
  type IPermission,
  type IPermissionsResponse,
  type ISaveGroupsRequest,
  type IUserWithGroup,
} from 'stores/AccessControlStore/AccessControlStore.type'

enum paths {
  ADMIN = '/api/v1/admin',
}

export interface IRole {
  groups: string[]
  userRole: { description: string; mandatory: boolean; role: string }
}

export const getRoles = (): Promise<IRole[]> => api.get(`${paths.ADMIN}/roles`)

export const getPermissions = (): Promise<IPermissionsResponse> => api.get(`${paths.ADMIN}/permissions`)

export const getPermissionsByRole = (role: string): Promise<{ permissions: IPermission[] }> =>
  api.get(`${paths.ADMIN}/role/${role}/permissions`)

export const getUsersByRole = (role: string): Promise<{ users: IUserWithGroup[] }> =>
  api.get(`${paths.ADMIN}/role/${role}/users`)

export const getGroups = (): Promise<{ ldapGroups: string[] }> => api.get(`${paths.ADMIN}/ldap-groups`)

export const saveGroupsForRole = (params: ISaveGroupsRequest) => api.put(`${paths.ADMIN}/roles`, params)
