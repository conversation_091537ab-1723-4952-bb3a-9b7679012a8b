import { axiosInstance as api } from 'shared/lib/axios'

export type IDataStorageType = 'NSI' | 'CALC_MODEL' | 'CALCULATIONS' | 'TELEMETRY' | 'AUDIT_LOGS'

export type IDataStorageDepthType = {
  storageType: {
    code: IDataStorageType // Обозначение типа хранилища
    description: string // Описание типа хранилища
    minNumberOfMonths: number // Минимальное значение в месяцах
    maxNumberOfMonths: number // Максимальное значение в месяцах
    defaultNumberOfMonths: number // Значение по умолчанию в месяцах
    order: number // Порядок отображения
  }
  depthInMonths: number // Текущая глубина хранения в месяцах
}

export interface IDataStorageDepthRequest {
  storageTypes: {
    storageType: IDataStorageType // Тип хранилища
    depthInMonths: number // Новое значение глубины хранения
  }[]
}

export const updateDataStorageDepth = (params: IDataStorageDepthRequest): Promise<IDataStorageDepthType[]> =>
  api.post('/api/v1/data-storage-depth', { ...params })

export const fetchDataStorageDepth = (): Promise<IDataStorageDepthType[]> => api.get('/api/v1/data-storage-depth')
