import { axiosInstance as api } from 'shared/lib/axios'

/**
 * Этот список представляет собой теги, используемые для организации настроек внешних систем.
 *
 * Названия тегов:
 * - "API": Название тега, связанного с настройками API Нептун.
 *   Примечание: Это название тега не возвращается из конечной точки 'api/v1/ext-settings/tags'.
 * - "SK11", "ISP", "MODES", "MAIL", "ISS_GES": Названия тегов, которые возвращаются по запросу.
 */
export type TagName = 'API' | 'SK11' | 'ISP' | 'MODES' | 'MAIL' | 'ISS_GES'
export interface Tag {
  tag: TagName
  name: string
  order: number
}
export const getAllSettingsTags = (): Promise<Tag[]> => api.get('/api/v1/ext-settings/tags')

export interface SettingItem {
  tag: string
  key: string
  value: string
  description: string
}
export const getSettingsByTag = (tag: TagName): Promise<SettingItem[]> => api.get(`/api/v1/ext-settings/tag/${tag}`)

interface ParamsEditSettings {
  // settings: Array<Pick<SettingItem, 'key' | 'value'>>
  settings: { key: string; value: string | null }[]
}
export const editSettingsTag = (params: ParamsEditSettings) => api.put('/api/v1/ext-settings', params)
