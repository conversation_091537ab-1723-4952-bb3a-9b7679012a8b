import { axiosInstance as api } from 'shared/lib/axios'

export interface TokenForExternalSystem {
  uid: string
  systemName: string
  createdDate: string
  validDate: string
  valid: boolean
}

export const fetchAllSettingsTokens = (): Promise<TokenForExternalSystem[]> => api.get('/api/v1/ext-api/token')

export const createSettingsToken = (params: { systemName: string }): Promise<TokenForExternalSystem> =>
  api.post('/api/v1/ext-api/token', { ...params })

export const invalidateSettingsToken = (uid: string) => api.put(`/api/v1/ext-api/token/${uid}/invalidate`)
