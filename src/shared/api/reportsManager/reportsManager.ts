import { IEditReportOutput } from 'entities/api/reportsManager.entities.ts'
import { Protocol } from 'entities/shared/common.entities'
import { axiosInstance as api } from 'shared/lib/axios'

export interface Item {
  code: string
  title: string
}

export interface ReportType {
  code: string
  title: string
}

export const getReportsTypes = (): Promise<ReportType[]> => api.get('/api/v1/reports/types')

export interface ReportListItem1 {
  id: number
  name: string
  description: string
}

export const getReports = (params: { reportType: string; sort?: string }): Promise<ReportListItem1[]> =>
  api.get('/api/v1/reports', { params })

export interface ReportDetails {
  description: string
  id?: number
  name: string
  reportType: ReportType | string
  settings?: {
    messageBody?: string
    plants?: {
      id: number
      name: string
    }[]
    unloadingDirectories?: string[]
    daySettings?: {
      dayOfWeek: string
      offsetDays: number
      unloadingNextDays: number
    }[]
    tableTitle?: boolean
    tableTitleTemplate?: string
    tableTitleRow?: number
    tableTitleColumn?: number
    tableHeader?: boolean
    tableHeaderRow?: number
    tableHeaderColumn?: number
    dateOffset?: boolean
    fileUnloading?: boolean
    avrcmTes?: boolean
    emails?: string[]
    fileNameTemplate?: string
    mailSubject?: string
    mailingReportDirectory?: string
    unloadingFormat?: string
    unloadingTemplate?: string
    templatePath?: string
    unloadingDirectory?: string
    firstHourRow?: number
    firstHourColumn?: number
    defaultMailingAttachment?: string
    sendFromCalculationPage?: boolean
    calculationPagePlants?: {
      id: number
      name: string
    }[]
    putDate: boolean
    dateRow: number
    dateColumn: number
    summaryFormat: boolean
    putHeader: boolean
  }
}

export const getReport = (id: number): Promise<ReportDetails> => api.get(`/api/v1/reports/${id}`)

export const editReport = (id: number, params: ReportDetails): Promise<IEditReportOutput> =>
  api.put(`/api/v1/reports/${id}`, params)

export const createReport = (params: ReportDetails): Promise<IEditReportOutput> => api.post('/api/v1/reports', params)

export const deleteReports = (params: { reportIds: number[] }): Promise<void> =>
  api.delete('/api/v1/reports', { data: params })

export const getUnloadingFormats = (): Promise<Item[]> => api.get('/api/v1/reports/unloading-formats')

export const getUnloadingTemplates = (): Promise<Item[]> => api.get('/api/v1/reports/unloading-templates')

interface NamePlaceholderItem {
  pattern: string
  desctiption: string
}

export interface NamePlaceholder {
  fileNamePlaceholders: NamePlaceholderItem[]
  tableNamePlaceholders: NamePlaceholderItem[]
}

export const getNamePlaceholders = (): Promise<NamePlaceholder> => api.get('/api/v1/reports/name-placeholders')

export interface IGetPlantsPlanDepartmentOutput {
  plantId: number
  name: string
}

export const getPlantsPlanDepartment = (): Promise<IGetPlantsPlanDepartmentOutput[]> =>
  api.get('/api/v1/plants/plan-department')

export interface IGetPlantsParticipateAvrchmOutput {
  plantId: number
  name: string
}

export const getPlantsParticipateAvrchm = (): Promise<IGetPlantsParticipateAvrchmOutput[]> =>
  api.get('/api/v1/plants/participate-avrchm')

export interface ValidateUnloadingParams {
  reportId: number
  reportType: string
  unloadingParameters: {
    fileNameTemplate: string
    calculationRequest?: {
      targetDate: string
      planingStage: string
    }
    calculationRequests?: {
      targetDate: string
      planingStage: string
    }[]
    templatePath?: string
    unloadingTemplate?: string
    unloadingDirectory?: string
    unloadingDirectories?: string[]
  }
}

export const validateUnloading = (params: ValidateUnloadingParams): Promise<Protocol> =>
  api.post('/api/v1/reports/validate-unloading', params)

interface UnloadingReportResponse extends Protocol {
  result: string
}

export const unloadingReport = (
  params: ValidateUnloadingParams,
  signal: AbortSignal,
): Promise<UnloadingReportResponse> => api.post('/api/v1/reports/unloading', params, { signal })

export const getTypesMailingAttachments = (): Promise<{ code: string; title: string }[]> =>
  api.get('/api/v1/reports/mailing-attachments')

export interface ValidateMailingParams {
  reportId?: number
  reportType?: string
  mailingParameters: {
    emails?: string[]
    mailingReportDirectory?: string | null
    fileNameTemplate?: string
    mailingAttachment?: string
    mailSubject?: string | null
    calculationRequest?: {
      targetDate: string
      planingStage: string
    }
    calculationRequests?: {
      targetDate: string
      planingStage: string
    }[]
    templatePath?: string
    unloadingFormat?: string
    unloadingTemplate?: string
    messageBody?: string
  }
}
export const validateMailing = (params: ValidateMailingParams): Promise<Protocol> =>
  api.post('/api/v1/reports/validate-mailing', params)

export const mailingReport = (params: ValidateMailingParams, signal: AbortSignal): Promise<Protocol> =>
  api.post('/api/v1/reports/mailing', params, { signal })

export interface IStageType {
  code: string
  title: string
  finished: boolean
}
export interface UnloadingDatesResponse {
  dateStages: {
    [date: string]: {
      stages: IStageType[]
      actualStage: Omit<IStageType, 'finished'>
    }
  }
}
export const getUnloadingDates = (id: number): Promise<UnloadingDatesResponse> =>
  api.get(`/api/v1/reports/${id}/unloading-dates`)
