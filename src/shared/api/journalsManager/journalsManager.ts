import { AxiosResponse } from 'axios'
import { prepareDateJournal } from 'entities/store/journalsStore.entities.ts'
import { axiosInstance as api } from 'shared/lib/axios'

export interface IUserActionsJournalParams {
  fromDate: Date | null
  toDate: Date | null
  status?: string | undefined
  interactionKind?: string | undefined
}

export interface IUserActionsJournalResponse {
  createdDate: string
  category: { name: string }
  login?: string
  departmentName?: string
  ip?: string
  description?: string
  id?: string
}

export interface IInteractionExternalSystemJournalResponse {
  createdDate: string
  externalSystem: string
  interactionKind?: { title: string }
  initiator: string
  departmentName: string
  status?: { title: string }
  description: string
}

export interface IInteractionExternalSystemJournalParams {
  fromDate: Date | null
  toDate: Date | null
  status?: string
}

export interface IUserActionsCategory {
  code: string
  name: string
}

export const getUserActionsJournal = (params: IUserActionsJournalParams): Promise<IUserActionsJournalResponse[]> =>
  api.get(`/api/v1/audit`, { params })

export const getUserActionsJournalXls = (
  params: IInteractionExternalSystemJournalParams,
): Promise<AxiosResponse<Blob>> => {
  const paramsTemp = JSON.parse(JSON.stringify(params) as string)
  const fromDate = prepareDateJournal(paramsTemp.fromDate, 'from')
  const toDate = prepareDateJournal(paramsTemp.toDate, 'to')

  return api({
    url: '/api/v1/audit/xls',
    method: 'GET',
    params: {
      ...params,
      fromDate,
      toDate,
    },
    responseType: 'blob',
    headers: { 'Access-Control-Expose-Headers': 'Content-Disposition' },
  })
}

export const getInteractionExternalSystem = (
  params: IInteractionExternalSystemJournalParams,
): Promise<IInteractionExternalSystemJournalResponse[]> => api.get(`/api/v1/ext-system-interaction`, { params })

export const getUserActionsCategory = (): Promise<IUserActionsCategory[]> => api.get(`/api/v1/audit/categories`)

export const getInteractionExternalSystemJournalXls = (
  params: IInteractionExternalSystemJournalParams,
): Promise<AxiosResponse<Blob>> => {
  const paramsTemp = JSON.parse(JSON.stringify(params) as string)
  const fromDate = prepareDateJournal(paramsTemp.fromDate, 'from')
  const toDate = prepareDateJournal(paramsTemp.toDate, 'to')

  return api({
    url: '/api/v1/ext-system-interaction/xls',
    method: 'GET',
    params: {
      ...params,
      fromDate,
      toDate,
    },
    responseType: 'blob',
    headers: { 'Access-Control-Expose-Headers': 'Content-Disposition' },
  })
}
