import { AxiosResponse } from 'axios'
import { IAcceptVaultOutput, ICalcPossibility } from 'entities/api/calculationsManager.entities'
import {
  ICalculationHistoryAcceptOutput,
  IGAESSpreadsheetCell,
  IGAESXlsReportParams,
  ILastStageForDates,
  TTaskType,
} from 'entities/api/gaesCalculationsManager.entities'
import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse'
import { axiosInstance as api } from 'shared/lib/axios'
import { isGodModeEnabled } from 'shared/lib/godMode'

export const getLastStageForDates = (
  calcDate: string,
  days?: number,
): Promise<ILastStageForDates | Record<string, never>> => {
  const url = days
    ? `/api/v1/calculation/stages/last?calcDate=${calcDate}&days=${days}`
    : `/api/v1/calculation/stages/last?calcDate=${calcDate}`

  return api.get(url)
}

export const getGaesArchiveSpreadsheetData = (
  plantId: number,
  month: string,
): Promise<IGetSpreadsheetData<IGAESSpreadsheetCell>> => {
  return api.get(`/api/v1/calculation/archive?plantId=${plantId}&month=${month}`)
}

export const getGaesCalculationSpreadsheetData = (
  plantId: number,
  calcDate: string,
  days?: number,
): Promise<IGetSpreadsheetData<IGAESSpreadsheetCell>> => {
  const params = new URLSearchParams({
    plantId: plantId.toString(),
    calcDate: calcDate,
    godMode: String(isGodModeEnabled()),
    ...(days && { days: days.toString() }),
  })

  return api.get(`/api/v1/calculation/gaes`, { params })
}

export const saveGaesCalculationSpreadsheetData = (objectPut: {
  plantId: number
  cells: any[]
}): Promise<{ warnings: string[] }> => {
  return api.put(`/api/v1/calculation/gaes`, { ...objectPut, godMode: isGodModeEnabled() })
}

export const getGAESCalcPossibility = (plantId: number, calcDate: string, days?: number): Promise<ICalcPossibility> => {
  const params = new URLSearchParams({
    plantId: plantId.toString(),
    calcDate: calcDate,
    ...(days && { days: days.toString() }),
    godMode: String(isGodModeEnabled()),
  })

  return api.get(`/api/v1/calculation/gaes/possibility`, { params })
}

export const getSourceData = (
  plantId: number,
  beginDate: string,
  taskType?: TTaskType,
  days?: number,
): Promise<{ warnings: string[] }> => {
  const data = {
    plantId: plantId.toString(),
    beginDate,
    godMode: isGodModeEnabled(),
    ...(days && { days: days.toString() }),
  }

  const params = {
    ...(taskType && { taskType }),
  }

  return api.post(`/api/v1/calculation/gaes/load`, data, { params })
}

export const calculateGeneration = (
  plantId: number,
  calcDate: string,
  days?: number,
): Promise<IGetSpreadsheetData<IGAESSpreadsheetCell>> => {
  const params = new URLSearchParams({
    plantId: plantId.toString(),
    calcDate: calcDate,
    ...(days && { days: days.toString() }),
  })

  return api.post(`/api/v1/calculation/gaes/generation`, null, { params })
}

export const getInitialSpreadsheetData = ({
  plantId,
  beginDate,
  days,
}: {
  plantId: number
  beginDate: string
  days?: number
}): Promise<IGetSpreadsheetData<IGAESSpreadsheetCell>> => {
  const putObject = {
    plantId,
    beginDate,
    godMode: isGodModeEnabled(),
    ...(days !== undefined && { days }),
  }

  return api.put(`/api/v1/calculation/gaes/init`, putObject)
}

export const acceptGaes = (
  forced: boolean,
  plantId: number,
  beginDate: string,
  acceptDates: string[],
): Promise<IAcceptVaultOutput[]> => {
  const params = {
    plantId: plantId,
    beginDate: beginDate,
    acceptDates: acceptDates,
    godMode: isGodModeEnabled(),
  }

  return api.put(`/api/v1/calculation/gaes/accept?forced=${forced}`, params)
}

export const setDisaccept = (
  plantId: number,
  beginDate: string,
  acceptDates: string[],
): Promise<IAcceptVaultOutput[]> => {
  const params = {
    plantId: plantId,
    beginDate: beginDate,
    acceptDates: acceptDates,
    godMode: isGodModeEnabled(),
  }

  return api.put(`/api/v1/calculation/gaes/disaccept`, params)
}

export const getHistoryAcceptData = (
  plantId: number,
  calcDate: string,
  days?: number,
): Promise<ICalculationHistoryAcceptOutput> => {
  const params = new URLSearchParams({
    plantId: plantId.toString(),
    calcDate: calcDate,
    ...(days && { days: days.toString() }),
  })

  return api.get(`/api/v1/calculation/gaes/accept/history`, { params })
}

export const getGAESXlsReport = (params: IGAESXlsReportParams): Promise<AxiosResponse<Blob>> => {
  return api.get('/api/v1/calculation/gaes/xls', {
    params,
    responseType: 'blob',
    headers: { 'Access-Control-Expose-Headers': 'Content-Disposition' },
    paramsSerializer: {
      indexes: null,
    },
  })
}
