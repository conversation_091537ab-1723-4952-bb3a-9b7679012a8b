import {
  GeneralParameter,
  getDepPlantsOutput,
  getHistoryWriteModesParams,
  IAddConsumptionSchedulesInput,
  IGetAcceptHistoryParamsOutput,
  IGetActiveStagesItemOutput,
  IGetConfigsOutput,
  IGetConsumptionFormulasItemOutput,
  IGetConsumptionSchedulesOutput,
  IGetEnergyDistrictForParams,
  IGetHistoryParamsOutput,
  IGetRecordModesForIdOutput,
  IGetRestrictionItemInput,
  ISaveDepPlantsInput,
  ISaveParamsInput,
  IWritePlanRguDataOutput,
  TParameterName,
  writePlanRguDataParams,
} from 'entities/api/calcModelPage.entities.ts'
import {
  ICalcModelAvrchmSettings,
  ICalcModelPlant,
  IGetEnergyDistrictOutput,
  IPlant,
} from 'entities/store/calcModelStore.entities.ts'
import { axiosInstance as api } from 'shared/lib/axios'

export const getDepPlants = (): Promise<getDepPlantsOutput[]> => {
  return api.get(`/api/v1/calc-model/dep-plants`)
}

interface GetBasePlanPlantsParams {
  date: string
  sortByOrder?: boolean
  endDate?: string
  planingStage?: string | null
}

interface GetGaesPlanPlantsParams extends GetBasePlanPlantsParams {
  plantType: 'GAES'
}

interface GetGesPlanPlantsParams extends GetBasePlanPlantsParams {
  plantType?: 'GES'
}

export interface ISavePlantParametersResponse {
  errors: string[]
  warnings: string[]
}

// Перегрузка для GAES
export function getPlanPlants(params: GetGesPlanPlantsParams): Promise<ICalcModelPlant[]>

// Перегрузка для GES
export function getPlanPlants(params: GetGaesPlanPlantsParams): Promise<IPlant[]>

// Универсальная реализация функции
export function getPlanPlants({
  date,
  plantType,
  sortByOrder,
  endDate,
  planingStage,
}: GetGesPlanPlantsParams | GetGaesPlanPlantsParams): Promise<IPlant[] | ICalcModelPlant[]> {
  const queryParams = new URLSearchParams({
    date: date,
    ...(plantType && { plantType }),
    ...(endDate && { endDate }),
    ...(planingStage && { planingStage }),
    ...(typeof sortByOrder !== 'undefined' && { sortByOrder: String(sortByOrder) }),
  })

  return api.get(`/api/v1/calc-model/plants`, { params: queryParams })
}

export const saveDepPlants = (res: ISaveDepPlantsInput): Promise<void> => {
  return api.patch(`/api/v1/calc-model/look-plants`, res)
}

export const deleteDepPlants = (res: number[]): Promise<void> => {
  return api.delete(`/api/v1/calc-model/look-plants`, { data: res })
}

export const saveCustomSort = (res: number[]): Promise<void> => {
  return api.post(`/api/v1/calc-model/plants/order`, res)
}

export const getRecordModesForId = (plantId: number): Promise<IGetRecordModesForIdOutput> => {
  return api.get(`/api/v1/calc-model/record-modes?plantId=${plantId}`)
}

export const saveRecordModesForId = (plantId: number, record: boolean): Promise<void> => {
  return api.put(`/api/v1/calc-model/record-modes`, { plantId, record })
}

export const getInMixingById = (queryParams: { plantId: number }): Promise<void> =>
  api.get('/api/v1/calc-model/plants/in-mixing', {
    params: queryParams,
  })

export const editInMixingById = (bodyParams: { plantId: number; mixing: boolean }): Promise<void> =>
  api.put('/api/v1/calc-model/plants/in-mixing', bodyParams)

export const saveParams = (
  res: ISaveParamsInput,
  date: string,
  forced: boolean,
): Promise<ISavePlantParametersResponse> => {
  return api.post(`/api/v1/plant/parameters?date=${date}&forced=${forced}`, res)
}

export const getConfigs = (plantId: number, date: string): Promise<IGetConfigsOutput> => {
  return api.get(`/api/v1/calc-model/plant/elements?plantId=${plantId}&date=${date}`)
}

export const getRestriction = (type: string | null): Promise<{ causes: IGetRestrictionItemInput[] }> => {
  return api.get(`/api/v1/restriction/causes?type=${type}`)
}
export const getGeneralParameters = (): Promise<GeneralParameter[]> => api.get('/api/v1/general-parameters')

export const editGeneralParameters = (params: Array<Omit<GeneralParameter, 'category'>>): Promise<GeneralParameter[]> =>
  api.post('/api/v1/general-parameters', { parameters: params })

export const getAcceptHistoryParams = (
  plantId: string,
  calcDate: string,
  planingStage: string,
): Promise<IGetAcceptHistoryParamsOutput[]> => {
  return api.get(
    `/api/v1/calculation/accept/history?plantId=${plantId}&calcDate=${calcDate}&planingStage=${planingStage}`,
  )
}

export const getHistoryParams = (
  plantId: number,
  parameterName: TParameterName,
): Promise<IGetHistoryParamsOutput[]> => {
  return api.get(`/api/v1/plant/${plantId}/parameter/${parameterName}`)
}

export const getConsumptionSchedules = (): Promise<IGetConsumptionSchedulesOutput[]> => {
  return api.get(`/api/v1/consumption-schedule`)
}

export const getEnergyDistrict = (): Promise<IGetEnergyDistrictOutput[]> => {
  return api.get(`/api/v1/energy-district`)
}

export const getActiveStages = (): Promise<IGetActiveStagesItemOutput[]> => {
  return api.get(`/api/v1/consumption-schedule/active-stages`)
}

export const getConsumptionFormulas = (districtId: number | null): Promise<IGetConsumptionFormulasItemOutput[]> => {
  return api.get(`/api/v1/consumption-schedule/consumption-formulas?districtId=${districtId}`)
}

export const getAllStages = (): Promise<IGetActiveStagesItemOutput[]> => {
  return api.get(`/api/v1/consumption-schedule/all-stages`)
}

export const getDepartmentsLevels = (): Promise<IGetActiveStagesItemOutput[]> => {
  return api.get(`/api/v1/consumption-schedule/department-levels`)
}

export const loadingTheFormula = (
  departmentLevel: string,
  planingStage: string,
  districtIspId: number,
): Promise<string> => {
  return api.get(
    `/api/v1/consumption-schedule/formula?departmentLevel=${departmentLevel}&planingStage=${planingStage}&districtIspId=${districtIspId}`,
  )
}

export const addConsumptionSchedules = (object: IAddConsumptionSchedulesInput): Promise<void> => {
  return api.post(`/api/v1/consumption-schedule`, object)
}

export const saveConsumptionSchedules = (object: IAddConsumptionSchedulesInput): Promise<void> => {
  return api.put(`/api/v1/consumption-schedule`, object)
}

export const trashConsumptionSchedules = (consumptionScheduleId: string): Promise<void> => {
  return api.delete(`/api/v1/consumption-schedule?consumptionScheduleId=${consumptionScheduleId}`)
}

export const deleteConsumptionScheduleMultiple = (params: { scheduleIds: number[] }): Promise<void> =>
  api.delete('/api/v1/consumption-schedule/multiple', { data: params })

export const getEnergyDistrictForParams = (): Promise<IGetEnergyDistrictForParams[]> => {
  return api.get(`/api/v1/consumption-schedule/energy-districts`)
}

export const getGeneratorParametersHistory = (
  generatorId: number,
  parameterName: string,
): Promise<IGetHistoryParamsOutput[]> => api.get(`/api/v1/generator/${generatorId}/parameter/${parameterName}`)

export const getRGUParametersHistory = (rguId: string, parameterName: string): Promise<IGetHistoryParamsOutput[]> =>
  api.get(`/api/v1/rgu/${rguId}/parameter/${parameterName}`)
export const writePlanRguData = (params: writePlanRguDataParams): Promise<IWritePlanRguDataOutput> =>
  api.post('/api/v1/calculation/write-plan-rgu-data', params)

export const getHistoryWriteModes = (
  params: getHistoryWriteModesParams,
): Promise<
  {
    status: string
    userFio: string
    departmentName: string
    updatedDate: Date
  }[]
> => api.get('/api/v1/calculation/write-history', { params })

export const getCalcModelAvrchmSettings = (targetDate: string): Promise<ICalcModelAvrchmSettings> =>
  api.get(`/api/v1/calc-model/avrchm/settings?targetDate=${targetDate}`)

export const updateCalcModelAvrchmSettings = (targetDate: string, payload: ICalcModelAvrchmSettings): Promise<void> =>
  api.put(`/api/v1/calc-model/avrchm/settings?targetDate=${targetDate}`, payload)
