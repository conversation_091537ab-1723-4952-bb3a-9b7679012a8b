import { AxiosResponse, GenericAbortSignal } from 'axios'
import { IGetAVRCHMSpreadsheetCell } from 'entities/api/calcModelPage.entities.ts'
import {
  AllowedZonedMode,
  IAcceptVault,
  IAcceptVaultOutput,
  IBaseCalcInput,
  ICalcPossibility,
  ICalculation,
  ICalculationLoadItemItem,
  ICalculationLoadOutput,
  ICalculationSummary,
  IDownloadAvrchmTesInput,
  IGetCalculationXlsInput,
  IGetDataUpdateVaultInput,
  IGetDataUpdateVaultOutput,
  IListStagesOutput,
  ILoadTelemetryOutput,
  ISaveDataStationInput,
  ISaveDataVaultInput,
  IStagesTypeOutput,
  ITelemetryOutput,
  IVaultActionInput,
  IVaultActionOutput,
  IWarningOutput,
  StagesParamsType,
  TStatusUpdateModalVault,
} from 'entities/api/calculationsManager.entities'
import { CalculationTaskType, IRguGenerationInput, PlanningStage, Protocol } from 'entities/shared/common.entities.ts'
import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse.ts'
import qs from 'qs'
import { axiosInstance as api } from 'shared/lib/axios'
import { isGodModeEnabled } from 'shared/lib/godMode'

export const uploadDailyOutput = (file: FormData): Promise<Protocol> => {
  return api.post(`/api/v1/calculation/import-daily-gen-target?godMode=${isGodModeEnabled()}`, file, {
    headers: { 'Content-Type': 'multipart/form-data' },
  })
}

export const getDataUpdateVault = ({
  calcDate,
  planingStage,
}: IGetDataUpdateVaultInput): Promise<IGetDataUpdateVaultOutput[]> => {
  return api.get(`/api/v1/calculation/summary/last-tasks?calcDate=${calcDate}&planingStage=${planingStage}`, {})
}

export const getTelemetry = (queryParams: any): Promise<ITelemetryOutput> =>
  api.get('/api/v1/telemetry', { params: queryParams })

export const getTelemetryByDate = (params: { plantId: number; targetDate: string }): Promise<ILoadTelemetryOutput> =>
  api.post('/api/v1/calculation/load/telemetry', params)

// Получение списка этапов планирования с актуальным этапом
export const getStages = (params: StagesParamsType): Promise<IStagesTypeOutput> =>
  api.get('/api/v1/calculation/stages', { params })

export const getListStages = (params: { calcDates: string[] }): Promise<IListStagesOutput> =>
  api.get('api/v1/calculation/stages-on-dates', {
    params,
    paramsSerializer: (params) => {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    },
  })

export const onDownloadTheSourceData = (
  plantId: number,
  targetDate: string,
  planingStage: PlanningStage,
  taskType: CalculationTaskType | null,
  ispDate: string,
): Promise<ICalculationLoadOutput> => {
  if (taskType) {
    return api.post(`/api/v1/calculation/load?taskType=${taskType}`, {
      plantId,
      targetDate,
      planingStage,
      ispDate,
      godMode: isGodModeEnabled(),
    })
  }

  return api.post(`/api/v1/calculation/load`, {
    plantId,
    targetDate,
    planingStage,
    ispDate,
    godMode: isGodModeEnabled(),
  })
}

export const downloadAvrchmTes = (params: IDownloadAvrchmTesInput): Promise<ICalculationLoadItemItem> =>
  api.post('/api/v1/calculation/load/avrchm', {
    ...params,
    godMode: isGodModeEnabled(),
  })

// Сохранение значений в расчётах
export const saveDataStation = (objectPost: ISaveDataStationInput): Promise<{ warnings: string[] }> => {
  return api.put('/api/v1/calculation', {
    ...objectPost,
    godMode: isGodModeEnabled(),
  })
}

export const rguGeneration = (postObject: IRguGenerationInput): Promise<IWarningOutput> => {
  return api.post('/api/v1/calculation/rgu-generation', {
    ...postObject,
    godMode: isGodModeEnabled(),
  })
}

export const getDataForStation = (
  plantId: number,
  calcDate: string,
  planingStage: PlanningStage,
  abortSignal: GenericAbortSignal,
): Promise<ICalculation> => {
  if (plantId === 0) throw Error('Загрузка данных с plantId=0 прервана, в связи с неактуальными данными.')

  return api.get(`/api/v1/calculation?plantId=${plantId}&calcDate=${calcDate}&planingStage=${planingStage}`, {
    signal: abortSignal,
  })
}
export const getDataVault = (
  calcDate: string,
  planingStage: PlanningStage,
  plantIds: number[],
): Promise<ICalculationSummary> =>
  api.get(`/api/v1/calculation/summary?plantIds=${plantIds}&calcDate=${calcDate}&planingStage=${planingStage}`)

export const saveDataVault = (res: ISaveDataVaultInput): Promise<IWarningOutput> => {
  return api.put(`/api/v1/calculation/summary`, { ...res, godMode: isGodModeEnabled() })
}

export const calcAllowedZones = (res: IBaseCalcInput): Promise<IWarningOutput> => {
  const url = '/api/v1/calculation/allowed-zones'

  return api.post(url, { ...res, godMode: isGodModeEnabled() })
}

export const calcGeneration = (res: IBaseCalcInput): Promise<IWarningOutput> => {
  return api.post('/api/v1/calculation/generation', { ...res, godMode: isGodModeEnabled() })
}

export const calcEnteringAllowedZones = (res: IBaseCalcInput, mode: AllowedZonedMode): Promise<IWarningOutput> => {
  return api.post(`/api/v1/calculation/entering-allowed-zones?mode=${mode}`, {
    ...res,
    godMode: isGodModeEnabled(),
  })
}

export const getCalcPossibility = (
  calcDate: string,
  planingStage: PlanningStage,
  plantId: number,
): Promise<ICalcPossibility> => {
  const params = new URLSearchParams({
    plantId: plantId.toString(),
    calcDate,
    planingStage: planingStage.toString(),
    godMode: String(isGodModeEnabled()),
  })

  return api.get(`/api/v1/calculation/possibility?${params.toString()}`)
}

export const getCalcPossibilityVault = (calcDate: string, planingStage: PlanningStage): Promise<ICalcPossibility> => {
  const params = new URLSearchParams({
    calcDate,
    planingStage: planingStage.toString(),
    godMode: String(isGodModeEnabled()),
  })

  return api.get(`/api/v1/calculation/summary/possibility?${params.toString()}`)
}

export const setAccept = (res: IBaseCalcInput, forced: boolean) => {
  return api.put<object, { message: any; details: any }>(`/api/v1/calculation/accept?forced=${forced}`, {
    ...res,
    godMode: String(isGodModeEnabled()),
  })
}

export const inizCalc = (params: IBaseCalcInput): Promise<ICalculation> =>
  api.put(`/api/v1/calculation/init`, { ...params, godMode: isGodModeEnabled() })

export const setDisaccept = (res: IBaseCalcInput) =>
  api.put<void, void>(`/api/v1/calculation/disaccept`, { ...res, godMode: isGodModeEnabled() })

export interface ICalcOptimizationParams {
  plantId: number
  targetDate: string
  planingStage: string
}
const paramsSerializer = (params: { [key: string]: string | number | boolean }) => {
  return Object.entries(params)
    .map(([key, value]) => {
      if (Array.isArray(value)) {
        return value.map((v) => `${encodeURIComponent(key)}=${encodeURIComponent(v)}`).join('&')
      }

      return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
    })
    .join('&')
}

export const getCalculationXls = (params: IGetCalculationXlsInput) => {
  return api.get<Promise<AxiosResponse<Blob>>>('/api/v1/calculation/xls', {
    params,
    responseType: 'blob',
    headers: { 'Access-Control-Expose-Headers': 'Content-Disposition' },
    paramsSerializer,
  })
}

export const calcOptimization = (payload: ICalcOptimizationParams) =>
  api.post<void, void>(`/api/v1/calculation/optimization`, { ...payload, godMode: isGodModeEnabled() })

export const calcLoadAllVault = (
  params: IVaultActionInput,
  taskType?: CalculationTaskType | null,
): Promise<IVaultActionOutput> => {
  if (taskType) {
    return api.post(`/api/v1/calculation/summary/load?taskType=${taskType}`, {
      ...params,
      godMode: isGodModeEnabled(),
    })
  }

  return api.post(` /api/v1/calculation/summary/load`, { ...params, godMode: isGodModeEnabled() })
}

export const calcOptimizationVault = (params: IVaultActionInput): Promise<IVaultActionOutput> => {
  return api.post(`/api/v1/calculation/summary/optimization`, { ...params, godMode: isGodModeEnabled() })
}

export const calcAllowedZonesVault = (params: IVaultActionInput): Promise<IVaultActionOutput> => {
  return api.post('/api/v1/calculation/summary/allowed-zones', {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const calcGenerationVault = (params: IVaultActionInput): Promise<IVaultActionOutput> => {
  return api.post('/api/v1/calculation/summary/generation', {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const enteringAllowedZones = (
  params: IVaultActionInput,
  mode: AllowedZonedMode,
): Promise<IVaultActionOutput> => {
  return api.post(`/api/v1/calculation/summary/entering-allowed-zones?mode=${mode}`, {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const rguGenerationVault = (params: IVaultActionInput): Promise<IVaultActionOutput> => {
  return api.post('/api/v1/calculation/summary/rgu-generation', {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const getGeneralStatusVault = (
  calcDate: string,
  planingStage: string,
): Promise<{ status: TStatusUpdateModalVault }> => {
  return api.get(`/api/v1/calculation/summary/general-status?calcDate=${calcDate}&planingStage=${planingStage}`)
}

export const acceptVault = (params: IAcceptVault, forced: boolean): Promise<IAcceptVaultOutput[]> => {
  return api.put(`/api/v1/calculation/summary/accept?forced=${forced}`, {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const disAcceptVault = (params: IAcceptVault): Promise<IAcceptVaultOutput[]> => {
  return api.put('/api/v1/calculation/summary/disaccept', {
    ...params,
    godMode: isGodModeEnabled(),
  })
}

export const getCalculationSummaryAvrchm = (
  calcDate: string,
  planingStage?: string,
): Promise<IGetSpreadsheetData<IGetAVRCHMSpreadsheetCell>> => {
  return api.get('/api/v1/calculation/summary/avrchm', {
    params: { calcDate, planingStage, godMode: isGodModeEnabled() },
  })
}
