import type { IUserDetails } from 'entities/store/auth.entities.ts'
import { axiosInstance as api } from 'shared/lib/axios'

enum paths {
  AUTH = '/api/v1/auth',
}
export const getDomains = () => {
  return api.get<object, { domains: any }>(`${paths.AUTH}/domains`)
}

interface UserDetailProps {
  login: string
  name: string
  roles: []
  departmentId: number
  department: string
}
export const login = (objectPost: { login: string; password: string }) => {
  return api.post<
    object,
    {
      access_token: string
      refresh_token: string
      userDetail: UserDetailProps
    }
  >(`/api/v1/auth/login`, objectPost)
}

export const logout = () => {
  return api.post(`/api/v1/auth/logout`)
}

export const refreshTokens = (refreshToken: string) => {
  return api.post(`/api/v1/auth/refresh`, { refreshToken })
}

export const getInfoUser = (): Promise<IUserDetails> => {
  return api.get(`/api/v1/auth/info`)
}

export const getAppVersion = () => {
  return api.get<object, any>(`/api/v1/common/version`)
}
