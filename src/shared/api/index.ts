import { accessControlService } from './accessControl'
import { calcModelManager } from './calcModelManager'
import { calcModelWerManager } from './calcModelWerManager'
import { calculationsManager } from './calculationsManager'
import { gaesCalculationsManager } from './gaesCalculationsManager'
import { journalsManger } from './journalsManager'
import { nsiManager } from './nsiManager'
import { reportsManager } from './reportsManager'
import { settingsManager } from './settingsManager'
import { userManager } from './userManager'

const api = {
  userManager,
  nsiManager,
  accessControlService,
  calcModelManager,
  calcModelWerManager,
  calculationsManager,
  settingsManager,
  reportsManager,
  journalsManger,
  gaesCalculationsManager,
}

export default api
