import { FC } from 'react'
import { IAllowedZone } from 'stores/CalculationsPageStore'

import cls from './AcceptableZones.module.scss'

interface AcceptableZonesProps {
  selectedCellAllowedZones: IAllowedZone[]
}

export const AcceptableZones: FC<AcceptableZonesProps> = (props) => {
  const { selectedCellAllowedZones } = props

  if (selectedCellAllowedZones.length > 0) {
    return (
      <div className={cls.tableFooter}>
        <div className={cls.titleZone}>Допустимые зоны для выбранного часа:</div>
        <div className={cls.zoneContainer}>
          {selectedCellAllowedZones.map((el) => {
            return (
              <div className={cls.zoneCell} key={`zone-${el.bottomLine}-${el.topLine}`}>
                {Number.isInteger(el?.bottomLine) ? el?.bottomLine : parseFloat(Number(el?.bottomLine).toFixed(3))}-
                {Number.isInteger(el?.topLine) ? el?.topLine : parseFloat(Number(el?.topLine).toFixed(3))}
              </div>
            )
          })}
        </div>
      </div>
    )
  }

  return <div className={cls.TableFooter}></div>
}
