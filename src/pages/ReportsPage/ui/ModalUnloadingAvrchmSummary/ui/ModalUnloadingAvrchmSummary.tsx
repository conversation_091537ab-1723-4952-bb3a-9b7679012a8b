import { Checkbox } from '@mui/material'
import { format, parse } from 'date-fns'
import { ModalReportProtocol } from 'features/ModalReportProtocol'
import { ChangeEvent, SetStateAction, useEffect, useMemo, useRef, useState } from 'react'
import api from 'shared/api/index'
import { classNames } from 'shared/lib/classNames/classNames'
import {
  IRow,
  IStageList,
  prepareReportAvrchmDataTable,
} from 'shared/lib/prepareReportDataTable/prepareReportAvrchmDataTable.ts'
import { Button } from 'shared/ui/Button'
import { DatePicker } from 'shared/ui/DatePicker'
import { Loader } from 'shared/ui/Loader'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { ModalWarning } from 'shared/ui/ModalWarning/ModalWarning'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { TextFieldWithPrompt } from 'shared/ui/TextFieldWithPrompt'
import { useStore } from 'stores/useStore'
import { Table } from 'widgets/Table'

import { ItemValue } from '../../ModalCreateEditReport/config/types'
import { INITIAL_VALUES } from '../model/const'
import { Props, Values } from '../model/types'
import cls from './ModalUnloadingAvrchmSummary.module.scss'

export const ModalUnloadingAvrchmSummary = (props: Props) => {
  const { reportType, id, onClose } = props
  const { reportsStore, notificationStore } = useStore()
  const { namePlaceholders, fetchNamePlaceholders, validateUnloading, validateData } = reportsStore

  // const [values, setValues] = useState<Values>(INITIAL_VALUES)
  const [values, setValues] = useState<Values>(INITIAL_VALUES)
  const [isLoadingValues, setIsLoadingValues] = useState(false)
  const [isVisibleModalProtocol, setIsVisibleModalProtocol] = useState(false)

  const fetchReport = async (id: number) => {
    try {
      setIsLoadingValues(true)
      const res = await api.reportsManager.getReport(id)
      setValues(
        (prev) =>
          ({
            ...prev,
            name: res.name,
            plants: res?.settings?.plants,
            fileNameTemplate: res?.settings?.fileNameTemplate,
            templatePath: res?.settings?.templatePath,
            unloadingDirectory: res?.settings?.unloadingDirectory,
            avrcmTes: res?.settings?.avrcmTes,
            summaryFormat: res?.settings?.summaryFormat ?? false,
            putHeader: res?.settings?.putHeader ?? false,
          }) as Values,
      )
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoadingValues(false)
    }
  }

  const fetchStages = async (date: Date) => {
    const calcDates = [format(date, 'yyyy-MM-dd')]
    try {
      const res = await api.calculationsManager.getListStages({
        calcDates,
      })
      prepareReportAvrchmDataTable(res.dateStages, setStagesList, setRows, setValues)
    } catch (error) {
      console.log(error)
    }
  }

  const fetchUnloadingDates = async (id: number) => {
    try {
      const res = await api.reportsManager.getUnloadingDates(id)
      prepareReportAvrchmDataTable(res.dateStages, setStagesList, setRows, setValues)
    } catch (error) {
      console.log(error)
    }
  }

  useEffect(() => {
    Promise.all([fetchNamePlaceholders(), fetchReport(id), fetchUnloadingDates(id)])
      .then(() => {})
      .catch((e) => console.log(e))

    return () => {
      reportsStore.validateData = { warnings: [], errors: [], result: '' }
    }
  }, [])

  const [validationInProgress, setValidationInProgress] = useState(false)

  const handleValidateUnload = async () => {
    const calculationRequest = {
      targetDate: format(parse(rows[0].date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: rows[0].planingStage,
    }

    const params = {
      reportId: id,
      reportType,
      unloadingParameters: {
        fileNameTemplate: values.fileNameTemplate,
        calculationRequest,
        templatePath: values.templatePath,
        unloadingDirectory: values.unloadingDirectory,
        avrcmTes: values.avrcmTes,
        summaryFormat: values.summaryFormat,
        putHeader: values.putHeader,
      },
    }

    try {
      setValidationInProgress(true)
      const data = await validateUnloading(params)

      if (data?.errors?.length || data?.warnings?.length) {
        setIsVisibleModalProtocol(true)
      } else if (!data?.errors?.length && !data?.warnings?.length) {
        handleUnload()
      }
    } catch (error) {
      console.log('error', error)
    } finally {
      setValidationInProgress(false)
    }
  }

  const unloadingReportAbortControllerRef = useRef<AbortController | null>(null)

  const [isUnloadingReport, setIsUnloadingReport] = useState(false)
  const [reportUnloadingName, setReportUnloadingName] = useState('')

  const handleUnload = async () => {
    unloadingReportAbortControllerRef.current?.abort()

    unloadingReportAbortControllerRef.current = new AbortController()

    const calculationRequest = {
      targetDate: format(parse(rows[0].date, 'dd.MM.yyyy', new Date()), 'yyyy-MM-dd'),
      planingStage: rows[0].planingStage,
    }

    const params = {
      reportId: id,
      reportType,
      unloadingParameters: {
        fileNameTemplate: values.fileNameTemplate,
        calculationRequest,
        templatePath: values.templatePath,
        unloadingDirectory: values.unloadingDirectory,
        avrcmTes: values.avrcmTes,
        summaryFormat: values.summaryFormat,
        putHeader: values.putHeader,
      },
    }

    const reportName = values.fileNameTemplate
      .replace(/<дата>/i, format(values.date, 'dd.MM.yyyy'))
      .replace(/<эп>/i, stagesList.find((item) => item.value === rows[0].planingStage)?.label ?? '')

    setReportUnloadingName(reportName)

    try {
      setIsUnloadingReport(true)
      setIsVisibleModalProtocol(false)

      const unloadingResponse = await api.reportsManager.unloadingReport(
        params,
        unloadingReportAbortControllerRef.current.signal,
      )

      if (unloadingResponse.errors.length) {
        notificationStore.addNotification({
          title: 'Ошибка',
          description: unloadingResponse.result,
          type: 'warning',
        })

        reportsStore.validateData = unloadingResponse
        setIsVisibleModalProtocol(true)
      } else {
        notificationStore.addNotification({
          title: '',
          description: unloadingResponse.result,
          type: 'success',
        })

        onClose()
      }
    } catch (error) {
      console.log(error)
    } finally {
      setIsUnloadingReport(false)
      setIsVisibleModalWarning(false)
    }
  }

  //
  // change values

  const handleChangevalue = (key: string, value: ItemValue) => {
    setValues((prev) => {
      const newValues = { ...prev, [key]: value }

      // При выключении переключателя "По форме Свод" автоматически выключаем "Выгружать шапку"
      if (key === 'summaryFormat' && !value) {
        newValues.putHeader = false
      }

      return newValues
    })
    reportsStore.validateData = { warnings: [], errors: [], result: '' }
  }

  const handleChangeDate = (value: Date) => {
    setValues((prev) => ({
      ...prev,
      date: value,
    }))

    reportsStore.validateData = { warnings: [], errors: [], result: '' }

    fetchStages(value)
  }

  //change values
  //

  //
  // table

  const refTableBlock = useRef<HTMLDivElement>(null)

  const [rows, setRows] = useState<IRow[]>([])
  const [stagesList, setStagesList] = useState<IStageList[]>([])

  const columns = [
    {
      name: 'date',
      title: 'Дата',
      width: 200,
    },
    {
      name: 'planingStage',
      title: 'Этап планирования',
      width: 200,
      editingEnabled: true,
      editType: 'select',
      selectDataFromRow: 'stages',
      render: (value: string, row: IRow) => {
        const currentItem = row.stages.find((el) => el.value === value)

        return <>{currentItem?.label ?? ''}</>
      },
    },
  ]

  const heightTable = useMemo(() => {
    return refTableBlock?.current?.clientHeight ?? 55
  }, [refTableBlock?.current?.clientHeight])

  //table
  //

  const [isVisibleModalWarning, setIsVisibleModalWarning] = useState(false)

  const handleClose = () => {
    if (isUnloadingReport) {
      setIsVisibleModalWarning(true)
    } else {
      onClose()
    }
  }

  const handleResetUnloading = () => {
    unloadingReportAbortControllerRef.current?.abort()

    setIsVisibleModalWarning(false)
    onClose()
  }

  const disabledLoad = validateData.errors.length > 0 || isLoadingValues

  return (
    <Modal
      open
      title='Выгрузка отчёта сводных данных АВРЧМ'
      maxWidth='md'
      onClose={handleClose}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            {disabledLoad && (
              <Button
                variant='outlined'
                onClick={() => setIsVisibleModalProtocol(true)}
                disabled={isLoadingValues || isUnloadingReport}
                className={cls.btnProtocol}
              >
                Протокол выгрузки
              </Button>
            )}
            <Button variant='outlined' onClick={handleClose} disabled={isUnloadingReport}>
              Отменить
            </Button>

            <LoadingButton
              className={cls.downloadButton}
              variant='contained'
              onClick={handleValidateUnload}
              disabled={disabledLoad}
              loading={validationInProgress || isUnloadingReport}
            >
              Выгрузить
            </LoadingButton>
          </div>
        </div>
      }
    >
      <>
        <div
          className={classNames(
            cls.wrapper,
            {
              [cls.wrapperLoading]: isLoadingValues || isUnloadingReport,
            },
            [],
          )}
        >
          {isLoadingValues || isUnloadingReport ? (
            <Loader />
          ) : (
            <>
              <div className={cls.row}>
                <div className={cls.labelRow}>Отчёт</div>
                <div className={cls.valueBlock}>
                  <TextField
                    style={{ width: '100%' }}
                    value={values?.name}
                    type='string'
                    disabled
                    onChange={(e) => handleChangevalue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Дата (план)</div>
                <div className={cls.valueBlock}>
                  <DatePicker value={values.date} setValue={handleChangeDate} />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.summaryFormat}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangevalue('summaryFormat', checked)
                    }
                    label='По форме Свод'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.putHeader}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangevalue('putHeader', checked)
                    }
                    disabled={!values.summaryFormat}
                    label='Выгружать шапку'
                  />
                </div>
              </div>
              <div ref={refTableBlock} className={cls.rowTable}>
                <Table
                  columns={columns}
                  rows={rows}
                  setRows={(val) => {
                    setRows(val as SetStateAction<IRow[]>)
                    reportsStore.resetValidateData()
                  }}
                  height={heightTable}
                  editMode
                  columnSearchDisabled={['date', 'planingStage']}
                />
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Станции</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values?.plants?.map((item) => item.name).join(', ')}
                    type='string'
                    multiline
                    className={cls.textFieldInput}
                    disabled
                    onChange={(e) => handleChangevalue('name', e.target.value)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>АВРЧМ ТЭС</div>
                <div className={cls.valueBlockFullWidth}>
                  <Checkbox
                    checked={values.avrcmTes}
                    disabled={values.summaryFormat}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => handleChangevalue('avrcmTes', e.target.checked)}
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон названия файла</div>
                <div className={cls.valueBlock}>
                  <TextFieldWithPrompt
                    className={cls.textFieldPropmt}
                    value={values.fileNameTemplate}
                    items={namePlaceholders}
                    onChange={(value: string) => handleChangevalue('fileNameTemplate', value)}
                    helperText=''
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Путь к шаблону</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values.templatePath}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangevalue('templatePath', e.target.value)}
                    placeholder='\\192.168.10.10\neptune\file.xls'
                  />
                </div>
              </div>
              <div className={cls.row}>
                <div className={cls.labelRow}>Директория для сохранения</div>
                <div className={cls.valueBlock}>
                  <TextField
                    value={values.unloadingDirectory}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangevalue('unloadingDirectory', e.target.value)}
                    placeholder='\\192.168.10.10\neptune'
                  />
                </div>
              </div>
            </>
          )}
        </div>

        {isVisibleModalProtocol && (
          <ModalReportProtocol
            handleClose={() => setIsVisibleModalProtocol(false)}
            handleAccept={handleUnload}
            acceptText='Выгрузить'
            closeText='Отменить'
            title='Выгрузка отчёта'
          />
        )}

        {isVisibleModalWarning && (
          <ModalWarning
            description={`Выгрузка отчёта "${reportUnloadingName}" будет прервана`}
            handleReset={handleResetUnloading}
            handleClose={() => setIsVisibleModalWarning(false)}
          />
        )}
      </>
    </Modal>
  )
}
