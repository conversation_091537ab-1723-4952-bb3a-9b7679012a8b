import AddIcon from '@mui/icons-material/Add'
import { Checkbox, IconButton } from '@mui/material'
import { useState } from 'react'
import { classNames } from 'shared/lib/classNames/classNames'
import { Button } from 'shared/ui/Button'
import { Icon } from 'shared/ui/Icon'
import { Modal } from 'shared/ui/Modal'
import { TextField } from 'shared/ui/TextField'

import s from './GroupsInput.module.scss'

export interface ValueGroups {
  value: string
  checked?: boolean
}

interface Props {
  values: ValueGroups[]
  maxItems?: number
  deleteMode?: boolean
  checkedMode?: boolean
  error?: boolean
  helperText?: string
  type?: 'directory'
  onChange: (values: ValueGroups[]) => void
}

export const GroupsInput = (props: Props) => {
  const { values, maxItems, deleteMode = true, checkedMode = false, error, helperText, type, onChange } = props

  const [isVisibleModal, setIsVisibleModal] = useState(false)
  const [newValue, setNewValue] = useState('')
  const [isError, setIsError] = useState('')

  const handleAddGroup = () => {
    if (!newValue.length) {
      setIsError('Поле должно быть заполнено')

      return
    }

    if (type) {
      let reg
      switch (type) {
        case 'directory':
          // const reg = new RegExp(/^\\(\\[a-zA-Zа-яА-Я0-9-.]+)+(\\[^\\\/:*?"<>|]+)+$/)
          reg = new RegExp(/^\\(\\[a-zA-Zа-яА-Я0-9-.]+)+(\\[^\\/:*?"<>|]+)+$/)
          if (!reg.test(newValue)) {
            setIsError('Пример \\\\192.168.10.10\\neptune')

            return
          }
      }
    }

    if (values.some((item: { value: string }) => item.value === newValue)) {
      setIsError('Такое значение уже сеществует')

      return
    }

    const newList = [...values]

    const prepareNewValue: ValueGroups = {
      value: newValue,
    }
    if (checkedMode) prepareNewValue.checked = true

    newList.push(prepareNewValue)
    setNewValue('')
    setIsVisibleModal(false)
    onChange(newList)
  }

  const handleDeleteItem = (index: number) => {
    const newList = [...values]
    newList.splice(index, 1)
    onChange(newList)
  }

  const handleChangeChecked = (index: number) => {
    onChange(
      values.map((item: ValueGroups, i: number) => ({
        ...item,
        checked: i !== index ? item.checked : !item.checked,
      })),
    )
  }

  return (
    <div className={s.groupBlock}>
      <Button
        variant='text'
        className={s.AddButton}
        onClick={() => {
          setIsVisibleModal(true)
        }}
        disabled={maxItems ? maxItems <= values.length : false}
      >
        Добавить
        <AddIcon width={16} />
      </Button>
      <ul className={classNames(s.groupList, { [s.groupListError]: error }, [])}>
        {values.map((item: ValueGroups, index: number) => (
          <li key={item.value} className={s.groupItem}>
            <span className={s.groupItemPath}>{item.value}</span>
            {deleteMode && (
              <IconButton
                className={s.btnTrash}
                onClick={() => {
                  handleDeleteItem(index)
                }}
              >
                <Icon width={13} name='trash' />
              </IconButton>
            )}
            {checkedMode && <Checkbox checked={item.checked} onChange={() => handleChangeChecked(index)} />}
          </li>
        ))}
      </ul>
      {helperText && <div className={s.errorLabel}>{helperText}</div>}
      {isVisibleModal && (
        <Modal
          open
          title='Добавить'
          maxWidth='md'
          onClose={() => {
            setIsVisibleModal(false)
          }}
          actions={
            <>
              <Button onClick={handleAddGroup} disabled={!!isError.length}>
                Ок
              </Button>
            </>
          }
        >
          <div className={s.container}>
            <TextField
              type='text'
              value={newValue}
              className={s.inputText}
              onChange={({ target }) => {
                setNewValue(target.value)
                setIsError('')
              }}
              placeholder='\\192.168.10.10\neptune'
              error={!!isError.length}
              helperText={isError}
            />
          </div>
        </Modal>
      )}
    </div>
  )
}
