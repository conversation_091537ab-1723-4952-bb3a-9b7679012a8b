.groupBlock {
  justify-self: end;
  display: flex;
  align-items: flex-end;
  flex-direction: column;
  width: 100%;
}

.groupList {
  width: 100%;
  min-height: 4em;
  max-height: 10em;
  height: fit-content;
  border-radius: 8px;
  border: 1px solid rgb(0 0 0 / 26%);
  background: inherit;
  overflow: auto;

  &Error {
    border: 1px solid var(--red-color);
  }
}

.groupItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.15em 0.5em;
  gap: 4px;
  font-size: 0.75rem;
  font-weight: 600;

  &Path {
    flex: 1;
  }
}

.btnTrash {
  &:hover {
    color: var(--red-color);
  }
}

.container {
  width: 300px;

  & .inputText {
    width: 100%;
  }
}

.errorLabel {
  color: #d32f2f;
  font-weight: 400;
  font-size: 0.75rem;
}
