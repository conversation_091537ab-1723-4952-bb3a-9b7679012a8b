import { Values } from './types'

export const LIST_DAYS_WEEK = [
  {
    value: 'MONDAY',
    label: 'Пн',
  },
  {
    value: 'TUESDAY',
    label: 'Вт',
  },
  {
    value: 'WEDNESDAY',
    label: 'Ср',
  },
  {
    value: 'THURSDAY',
    label: 'Чт',
  },
  {
    value: 'FRIDAY',
    label: 'Пт',
  },
  {
    value: 'SATURDAY',
    label: 'Сб',
  },
  {
    value: 'SUNDAY',
    label: 'Вс',
  },
]

export const EMPTY_VALUE = {
  oldValue: null,
  newValue: null,
}

export const INITIAL_VALUES_PLAN_GENERATION: Values = {
  name: EMPTY_VALUE,
  description: EMPTY_VALUE,
  plants: {
    oldValue: [],
    newValue: [],
  },
  fileNameTemplate: EMPTY_VALUE,
  mailSubject: EMPTY_VALUE,
  avrcmTes: {
    oldValue: false,
    newValue: false,
  },
  emails: {
    oldValue: [],
    newValue: [],
  },
  unloadingFormat: {
    oldValue: 'XLSX',
    newValue: 'XLSX',
  },
  unloadingTemplate: {
    oldValue: 'DATE_PLANTS',
    newValue: 'DATE_PLANTS',
  },
  unloadingDirectories: {
    oldValue: [],
    newValue: [],
  },
  offsetDaysTurnedOn: {
    oldValue: false,
    newValue: false,
  },
  offsetDaysMONDAY: EMPTY_VALUE,
  offsetDaysTUESDAY: EMPTY_VALUE,
  offsetDaysWEDNESDAY: EMPTY_VALUE,
  offsetDaysTHURSDAY: EMPTY_VALUE,
  offsetDaysFRIDAY: EMPTY_VALUE,
  offsetDaysSATURDAY: EMPTY_VALUE,
  offsetDaysSUNDAY: EMPTY_VALUE,
  tableTitle: {
    oldValue: true,
    newValue: true,
  },
  tableTitleTemplate: EMPTY_VALUE,
  tableTitleRow: {
    oldValue: 2,
    newValue: 2,
  },
  tableTitleColumn: {
    oldValue: 1,
    newValue: 1,
  },
  tableHeader: {
    oldValue: true,
    newValue: true,
  },
  tableHeaderRow: {
    oldValue: 3,
    newValue: 3,
  },
  firstHourRow: {
    oldValue: 4,
    newValue: 4,
  },
  firstHourColumn: {
    oldValue: 1,
    newValue: 1,
  },
  unloadingNextDaysTurnedOn: {
    oldValue: false,
    newValue: false,
  },
  unloadingNextDaysMONDAY: EMPTY_VALUE,
  unloadingNextDaysTUESDAY: EMPTY_VALUE,
  unloadingNextDaysWEDNESDAY: EMPTY_VALUE,
  unloadingNextDaysTHURSDAY: EMPTY_VALUE,
  unloadingNextDaysFRIDAY: EMPTY_VALUE,
  unloadingNextDaysSATURDAY: EMPTY_VALUE,
  unloadingNextDaysSUNDAY: EMPTY_VALUE,
  mailingReportDirectory: EMPTY_VALUE,
  defaultMailingAttachment: {
    oldValue: 'NEW_REPORT',
    newValue: 'NEW_REPORT',
  },
  sendFromCalculationPage: {
    oldValue: false,
    newValue: false,
  },
  calculationPagePlants: {
    oldValue: [],
    newValue: [],
  },
  putDate: {
    oldValue: false,
    newValue: false,
  },
  dateRow: {
    oldValue: 1,
    newValue: 1,
  },
  dateColumn: {
    oldValue: 1,
    newValue: 1,
  },
  summaryFormat: {
    oldValue: false,
    newValue: false,
  },
  putHeader: {
    oldValue: false,
    newValue: false,
  },
}

export const INITIAL_VALUES_AVRCHM: Values = {
  name: EMPTY_VALUE,
  description: EMPTY_VALUE,
  plants: {
    oldValue: [],
    newValue: [],
  },
  fileNameTemplate: EMPTY_VALUE,
  mailSubject: EMPTY_VALUE,
  avrcmTes: {
    oldValue: false,
    newValue: false,
  },
  emails: {
    oldValue: [],
    newValue: [],
  },
  templatePath: EMPTY_VALUE,
  unloadingDirectory: EMPTY_VALUE,
  firstHourRow: {
    oldValue: 1,
    newValue: 1,
  },
  firstHourColumn: {
    oldValue: 1,
    newValue: 1,
  },
  defaultMailingAttachment: {
    oldValue: 'NEW_REPORT',
    newValue: 'NEW_REPORT',
  },
  sendFromCalculationPage: {
    oldValue: false,
    newValue: false,
  },
  calculationPagePlants: {
    oldValue: [],
    newValue: [],
  },
  putDate: {
    oldValue: false,
    newValue: false,
  },
  dateRow: {
    oldValue: 1,
    newValue: 1,
  },
  dateColumn: {
    oldValue: 1,
    newValue: 1,
  },
  summaryFormat: {
    oldValue: false,
    newValue: false,
  },
  putHeader: {
    oldValue: false,
    newValue: false,
  },
}

export const VARIABLES_OFFSET_DAYS = [
  'offsetDaysMONDAY',
  'offsetDaysTUESDAY',
  'offsetDaysWEDNESDAY',
  'offsetDaysTHURSDAY',
  'offsetDaysFRIDAY',
  'offsetDaysSATURDAY',
  'offsetDaysSUNDAY',
]

export const VARIABLES_UNLOADING_NEXT_DAYS = [
  'unloadingNextDaysMONDAY',
  'unloadingNextDaysTUESDAY',
  'unloadingNextDaysWEDNESDAY',
  'unloadingNextDaysTHURSDAY',
  'unloadingNextDaysFRIDAY',
  'unloadingNextDaysSATURDAY',
  'unloadingNextDaysSUNDAY',
]

export const REQUIRED_FIELD_KEYSS_PLAN_GENERATION = new Set([
  'name',
  'plants',
  'fileNameTemplate',
  'unloadingDirectories',
  ...VARIABLES_OFFSET_DAYS,
  ...VARIABLES_UNLOADING_NEXT_DAYS,
])

export const REQUIRED_FIELD_KEYSS_AVRCHM_SUMMARY = new Set([
  'name',
  'plants',
  'fileNameTemplate',
  'templatePath',
  'unloadingDirectory',
  'firstHourRow',
])
