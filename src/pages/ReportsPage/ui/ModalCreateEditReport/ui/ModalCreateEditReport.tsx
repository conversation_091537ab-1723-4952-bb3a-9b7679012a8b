import { Checkbox } from '@mui/material'
import { PlanningStage } from 'entities/shared/common.entities.ts'
import { observer } from 'mobx-react'
import { GroupsInput, ValueGroups } from 'pages/ReportsPage/ui/GroupsInput/GroupsInput'
import { ChangeEvent, type SyntheticEvent, useEffect, useMemo, useState } from 'react'
import { useHotkeys } from 'react-hotkeys-hook'
import api from 'shared/api/index'
import { ReportDetails } from 'shared/api/reportsManager/reportsManager'
import { AnyObject, areArraysEqual, areObjectsEqual, isObject } from 'shared/lib/areEqual'
import { classNames } from 'shared/lib/classNames/classNames'
import { Autocomplete } from 'shared/ui/Autocomplete'
import { AutocompleteEmails, AutocompleteEmailsProps } from 'shared/ui/AutocompleteEmails'
import { Button } from 'shared/ui/Button'
import { Loader } from 'shared/ui/Loader'
import { LoadingButton } from 'shared/ui/LoadingButton'
import { Modal } from 'shared/ui/Modal'
import { Select } from 'shared/ui/Select'
import { Switch } from 'shared/ui/Switch'
import { TextField } from 'shared/ui/TextField'
import { TextFieldWithPrompt } from 'shared/ui/TextFieldWithPrompt'
import { IDepAndAvrchmsItem } from 'stores/ReportsStore/ReportsStore.ts'
import { useStore } from 'stores/useStore'

import { LIST_DAYS_WEEK, VARIABLES_OFFSET_DAYS, VARIABLES_UNLOADING_NEXT_DAYS } from '../config/const'
import { ItemValue, Props, Value, Values } from '../config/types'
import { getInitialValues, prepareValue } from '../lib'
import { validation } from '../lib/validation'
import cls from './ModalCreateReport.module.scss'

export interface IRow {
  planingStage: PlanningStage
  date?: string
  stages: { value: PlanningStage; label: string }[]
}

export interface IValues extends ReportDetails {
  mailingAttachment?: string
  fileNameTemplate?: string
  templatePath?: {
    oldValue?: string
    newValue?: string
  }
  mailingReportDirectory?: {
    oldValue?: string
    newValue?: string
  }
  emails?: string
  mailSubject?: string
  avrcmTes?: boolean
  date?: number | Date
  plants?: { name: string }[]
  messageBody?: Value<string>
  summaryFormat?: boolean
  putHeader?: boolean
}

export const ModalCreateEditReport = observer((props: Props) => {
  const { type, reportType = '', id, fetchReports, handleClose } = props
  const { reportsStore } = useStore()
  const {
    unloadingFormatList,
    unloadingTemplateList,
    namePlaceholders,
    tableNamePlaceholders,
    plantsPlanDepartment,
    plantsParticipateAvrchm,
    typesMailingAttachment,
    fetchUnloadingFormatList,
    fetchUnloadingTemplateList,
    fetchNamePlaceholders,
    fetchPlantsPlanDepartment,
    fetchPlantsParticipateAvrchm,
    fetchMailingAttachment,
  } = reportsStore
  const [values, setValues] = useState<Values>(() => getInitialValues(reportType))
  const [isLoadingValues, setIsLoadingValues] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isDisabledBtnSave, setIsDisabledBtnSave] = useState(true)

  const fetchReport = async (id: number) => {
    try {
      setIsLoadingValues(true)
      const res = await api.reportsManager.getReport(id)
      const plants = res?.settings?.plants ? prepareValue(res?.settings?.plants) : { oldValue: [], newValue: [] }
      const newValues: Values = {
        name: prepareValue(res.name),
        description: prepareValue(res.description),
        plants: plants as unknown as Value,
        messageBody: res?.settings?.messageBody
          ? prepareValue(res?.settings?.messageBody)
          : { oldValue: '', newValue: '' },
        fileNameTemplate: res?.settings?.fileNameTemplate
          ? prepareValue(res?.settings?.fileNameTemplate)
          : { oldValue: '', newValue: '' },
        mailSubject: res?.settings?.mailSubject
          ? prepareValue(res?.settings?.mailSubject)
          : { oldValue: '', newValue: '' },
        emails: res?.settings?.emails ? prepareValue(res?.settings?.emails) : { oldValue: '', newValue: '' },
        avrcmTes: res?.settings?.avrcmTes ? prepareValue(res?.settings?.avrcmTes) : { oldValue: '', newValue: '' },
        firstHourRow: res?.settings?.firstHourRow
          ? prepareValue(res?.settings?.firstHourRow)
          : {
              oldValue: 4,
              newValue: 4,
            },
        firstHourColumn: res?.settings?.firstHourColumn
          ? prepareValue(res?.settings?.firstHourColumn)
          : {
              oldValue: 1,
              newValue: 1,
            },
        tableTitleColumn: res?.settings?.tableTitleColumn
          ? prepareValue(res?.settings?.tableTitleColumn)
          : {
              oldValue: 1,
              newValue: 1,
            },
        tableTitleRow: res?.settings?.tableTitleRow
          ? prepareValue(res?.settings?.tableTitleRow)
          : {
              oldValue: 2,
              newValue: 2,
            },
        defaultMailingAttachment: res?.settings?.defaultMailingAttachment
          ? prepareValue(res?.settings?.defaultMailingAttachment)
          : {
              oldValue: 'NEW_REPORT',
              newValue: 'NEW_REPORT',
            },
        sendFromCalculationPage: prepareValue<boolean>(!!res?.settings?.sendFromCalculationPage),
        calculationPagePlants: res?.settings?.calculationPagePlants
          ? prepareValue(res?.settings?.calculationPagePlants)
          : {
              oldValue: [],
              newValue: [],
            },
        putDate: res?.settings?.putDate
          ? prepareValue(res?.settings?.putDate)
          : {
              oldValue: false,
              newValue: false,
            },
        dateRow: res?.settings?.dateRow
          ? prepareValue(res?.settings?.dateRow)
          : {
              oldValue: 1,
              newValue: 1,
            },
        dateColumn: res?.settings?.dateColumn
          ? prepareValue(res?.settings?.dateColumn)
          : {
              oldValue: 1,
              newValue: 1,
            },
        summaryFormat: res?.settings?.summaryFormat
          ? prepareValue(res?.settings?.summaryFormat)
          : {
              oldValue: false,
              newValue: false,
            },
        putHeader: res?.settings?.putHeader
          ? prepareValue(res?.settings?.putHeader)
          : {
              oldValue: false,
              newValue: false,
            },
      }

      let newValuesByType: Values = {}
      const unloadingDirectories = res?.settings?.unloadingDirectories
        ? prepareValue(
            res?.settings?.unloadingDirectories.map((item) => ({
              value: item,
            })),
          )
        : { oldValue: '', newValue: '' }
      if (reportType === 'PLAN_GENERATION') {
        newValuesByType = {
          tableTitle:
            typeof res?.settings?.tableTitle === 'boolean'
              ? prepareValue(res?.settings?.tableTitle)
              : { oldValue: false, newValue: false },
          tableTitleTemplate: res?.settings?.tableTitleTemplate
            ? prepareValue(res?.settings?.tableTitleTemplate)
            : { oldValue: '', newValue: '' },
          tableTitleRow: res?.settings?.tableTitleRow
            ? prepareValue(res?.settings?.tableTitleRow)
            : {
                oldValue: 2,
                newValue: 2,
              },
          tableTitleColumn: res?.settings?.tableTitleColumn
            ? prepareValue(res?.settings?.tableTitleColumn)
            : {
                oldValue: 1,
                newValue: 1,
              },
          tableHeader:
            typeof res?.settings?.tableHeader === 'boolean'
              ? prepareValue(res?.settings?.tableHeader)
              : { oldValue: false, newValue: false },
          tableHeaderRow: res?.settings?.tableHeaderRow
            ? prepareValue(res?.settings?.tableHeaderRow)
            : {
                oldValue: 3,
                newValue: 3,
              },
          unloadingFormat: res?.settings?.unloadingFormat
            ? prepareValue(res?.settings?.unloadingFormat)
            : { oldValue: '', newValue: '' },
          unloadingTemplate: res?.settings?.unloadingTemplate
            ? prepareValue(res?.settings?.unloadingTemplate)
            : { oldValue: '', newValue: '' },
          unloadingDirectories: unloadingDirectories as unknown as Value,
          offsetDaysTurnedOn:
            typeof res?.settings?.dateOffset === 'boolean'
              ? prepareValue(res?.settings?.dateOffset)
              : { oldValue: false, newValue: false },
          unloadingNextDaysTurnedOn:
            typeof res?.settings?.fileUnloading === 'boolean'
              ? prepareValue(res?.settings?.fileUnloading)
              : { oldValue: false, newValue: false },
          mailingReportDirectory: res?.settings?.mailingReportDirectory
            ? prepareValue(res?.settings?.mailingReportDirectory)
            : { oldValue: null, newValue: null },
        }
        const daySettings = res?.settings?.daySettings ? res?.settings?.daySettings : []

        for (const item of daySettings) {
          newValuesByType[`offsetDays${item.dayOfWeek}`] = prepareValue(item.offsetDays)
          newValuesByType[`unloadingNextDays${item.dayOfWeek}`] = prepareValue(item.unloadingNextDays)
        }
      } else if (reportType === 'AVRCHM_SUMMARY') {
        newValuesByType = {
          templatePath: res?.settings?.templatePath
            ? prepareValue(res?.settings?.templatePath)
            : { oldValue: '', newValue: '' },
          unloadingDirectory: res?.settings?.unloadingDirectory
            ? prepareValue(res?.settings?.unloadingDirectory)
            : { oldValue: '', newValue: '' },
        }
      }

      setValues({ ...newValues, ...newValuesByType })
      setIsDisabledBtnSave(true)
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoadingValues(false)
    }
  }

  const getStationsRequest = async () => {
    switch (reportType) {
      case 'AVRCHM_SUMMARY':
        await fetchPlantsParticipateAvrchm()
        break
      case 'PLAN_GENERATION':
        await fetchPlantsPlanDepartment()
        break
      default:
        break
    }
  }

  useEffect(() => {
    Promise.all([
      fetchUnloadingFormatList(),
      fetchUnloadingTemplateList(),
      fetchNamePlaceholders(),
      fetchMailingAttachment(),
      getStationsRequest(),
    ]).catch((e) => console.log(e))

    if (type === 'edit' && id) {
      fetchReport(id)
    }

    return () => {
      reportsStore.plantsParticipateAvrchm = []
      reportsStore.plantsPlanDepartment = []
    }
  }, [])

  const handleChangeValue = (key: string, value: ItemValue) => {
    resetErrors()
    const newValues = {
      ...values,
      [key]: { ...values[key], newValue: value },
    }

    if (key === 'unloadingFormat' && value !== 'XLSX') {
      newValues.unloadingTemplate.newValue = 'DATE_PLANTS'
    } else if (key === 'offsetDaysTurnedOn' && !value) {
      newValues.offsetDaysTurnedOn.newValue = false
    } else if (key === 'tableTitle' && !value) {
      newValues.tableTitle.newValue = false
    } else if (key === 'tableHeader' && !value) {
      newValues.tableHeader.newValue = false
    } else if (key === 'summaryFormat' && !value) {
      newValues.putHeader.newValue = false
    } else if (key === 'putDate' && !value) {
      newValues.putDate.newValue = false
    }

    setValues(newValues)

    let thereAreChanges = false

    for (const key in newValues) {
      if (Array.isArray(newValues[key].oldValue) && Array.isArray(newValues[key].newValue)) {
        if (!areArraysEqual(newValues[key].oldValue as string[], newValues[key].newValue as string[])) {
          thereAreChanges = true
          break
        }
        continue
      }
      const nValue = newValues[key]?.newValue as unknown as AnyObject
      const oValue = newValues[key]?.oldValue as unknown as AnyObject
      if (isObject(oValue) && isObject(nValue)) {
        if (!areObjectsEqual(oValue, nValue)) {
          thereAreChanges = true
          break
        }
        continue
      }
      if (newValues[key].oldValue !== newValues[key].newValue) {
        thereAreChanges = true
        break
      }
    }

    if (thereAreChanges) setIsDisabledBtnSave(false)
    else setIsDisabledBtnSave(true)

    if (errors[key] !== undefined)
      setErrors((prev) => {
        const newObj = { ...prev }
        delete newObj[key]

        let offsetDaysIsValid = true
        for (const keyItem of Object.keys(newObj)) {
          if (VARIABLES_OFFSET_DAYS.includes(keyItem)) {
            offsetDaysIsValid = false
            break
          }
        }
        if (offsetDaysIsValid) delete newObj.offsetDaysTurnedOn

        let unloadingNextDaysIsValid = true
        for (const keyItem of Object.keys(newObj)) {
          if (VARIABLES_UNLOADING_NEXT_DAYS.includes(keyItem)) {
            unloadingNextDaysIsValid = false
            break
          }
        }
        if (unloadingNextDaysIsValid) delete newObj.unloadingNextDaysTurnedOn

        if (key === 'offsetDaysTurnedOn') {
          delete newObj.offsetDays

          for (const item of VARIABLES_OFFSET_DAYS) {
            delete newObj[item]
          }
        }
        if (key === 'unloadingNextDaysTurnedOn') {
          delete newObj.unloadingNextDays

          for (const item of VARIABLES_UNLOADING_NEXT_DAYS) {
            delete newObj[item]
          }
        }

        return newObj
      })
    if (key === 'tableTitle' || key === 'tableHeader')
      setErrors((prev) => {
        const newObj = { ...prev }
        if (key === 'tableTitle' && errors['tableTitleTemplate'] !== undefined) {
          delete newObj.tableTitleTemplate
        }
        if (key === 'tableTitle' && errors['tableTitleRow'] !== undefined) {
          delete newObj.tableTitleRow
        }
        if (key === 'tableTitle' && errors['tableTitleColumn'] !== undefined) {
          delete newObj.tableTitleRow
        }
        if (key === 'tableHeader' && errors['tableHeaderRow'] !== undefined) {
          delete newObj.tableHeaderRow
        }

        return newObj
      })
  }

  const [isLoadingCreateEdit, setIsLoadingCreateEdit] = useState(false)

  const handleSave = async () => {
    const [isValid, errors] = validation(reportType, values)
    setErrors(errors as unknown as Record<string, string>)
    if (!isValid) return

    const params: ReportDetails = {
      description: values.description.newValue as string,
      name: values.name.newValue as string,
      reportType: reportType,
      settings: {
        plants: [],
        daySettings: [],
        unloadingDirectories: [],
        putDate: values?.putDate?.newValue as boolean,
        dateRow: values?.dateRow?.newValue as number,
        dateColumn: values?.dateColumn?.newValue as number,
        summaryFormat: values?.summaryFormat?.newValue as boolean,
        putHeader: values?.putHeader?.newValue as boolean,
      },
    }

    if (reportType === 'PLAN_GENERATION') {
      const daySettings = []
      for (const dayWeek of LIST_DAYS_WEEK) {
        daySettings.push({
          dayOfWeek: dayWeek.value,
          offsetDays: Number(values[`offsetDays${dayWeek.value}`].newValue),
          unloadingNextDays: Number(values[`unloadingNextDays${dayWeek.value}`].newValue),
        })
      }

      const unloadingDirectories =
        (values?.unloadingDirectories?.newValue as unknown as {
          value: string
        }[]) ?? []

      params.settings = {
        dateOffset: values.offsetDaysTurnedOn.newValue as boolean,
        fileUnloading: values.unloadingNextDaysTurnedOn.newValue as boolean,
        daySettings,
        avrcmTes: values.avrcmTes.newValue as boolean,
        emails: values.emails.newValue as string[],
        fileNameTemplate: values.fileNameTemplate.newValue as string,
        mailSubject: values.mailSubject.newValue as string,
        mailingReportDirectory: values.mailingReportDirectory.newValue as string,
        plants: values?.plants?.newValue as unknown as {
          id: number
          name: string
        }[],
        unloadingDirectories: unloadingDirectories?.map((item: { value: string }) => item.value),
        unloadingFormat: values.unloadingFormat.newValue as string,
        unloadingTemplate: values.unloadingTemplate.newValue as string,
        tableTitle: values.tableTitle.newValue as boolean,
        tableTitleTemplate: values.tableTitleTemplate.newValue as string,
        tableTitleRow: values.tableTitleRow.newValue as number,
        tableTitleColumn: values.tableTitleColumn.newValue as number,
        tableHeader: values.tableHeader.newValue as boolean,
        tableHeaderRow: values.tableHeaderRow.newValue as number,
        firstHourRow: values.firstHourRow.newValue as number,
        firstHourColumn: values?.firstHourColumn?.newValue as number,
        defaultMailingAttachment: values?.defaultMailingAttachment?.newValue as string,
        sendFromCalculationPage: values?.sendFromCalculationPage?.newValue as boolean,
        calculationPagePlants: values?.calculationPagePlants?.newValue as unknown as {
          id: number
          name: string
        }[],
        messageBody: values?.messageBody?.newValue as string,
        putDate: values?.putDate?.newValue as boolean,
        dateRow: values?.dateRow?.newValue as number,
        dateColumn: values?.dateColumn?.newValue as number,
        summaryFormat: values?.summaryFormat?.newValue as boolean,
        putHeader: values?.putHeader?.newValue as boolean,
      }
    } else {
      params.settings = {
        plants: values.plants.newValue as unknown as {
          id: number
          name: string
        }[],
        unloadingDirectory: values.unloadingDirectory.newValue as string,
        daySettings: [],
        fileNameTemplate: values.fileNameTemplate.newValue as string,
        mailSubject: values.mailSubject.newValue as string,
        avrcmTes: values.avrcmTes.newValue as boolean,
        emails: values.emails.newValue as string[],
        firstHourRow: values.firstHourRow.newValue as number,
        firstHourColumn: values?.firstHourColumn?.newValue as number,
        templatePath: values?.templatePath?.newValue as string,
        defaultMailingAttachment: values?.defaultMailingAttachment?.newValue as string,
        sendFromCalculationPage: values?.sendFromCalculationPage?.newValue as boolean,
        calculationPagePlants: values?.calculationPagePlants?.newValue as unknown as {
          id: number
          name: string
        }[],
        messageBody: values?.messageBody?.newValue as string,
        putDate: values?.putDate?.newValue as boolean,
        dateRow: values?.dateRow?.newValue as number,
        dateColumn: values?.dateColumn?.newValue as number,
        summaryFormat: values?.summaryFormat?.newValue as boolean,
        putHeader: values?.putHeader?.newValue as boolean,
      }
    }

    try {
      setIsLoadingCreateEdit(true)
      if (type === 'create') await api.reportsManager.createReport(params)
      if (type === 'edit') {
        if (id) {
          params.id = id
          await api.reportsManager.editReport(id, params)
        }
      }
      handleClose && handleClose()
      fetchReports && (await fetchReports())
    } catch (error) {
      console.log(error)
    } finally {
      setIsLoadingCreateEdit(false)
    }
  }

  const resetErrors = () => {
    setErrors({})
  }

  const handleReset = () => {
    setValues((prev: Values) => {
      const newValues: Values = {}

      for (const key in prev) {
        newValues[key] = {
          oldValue: prev[key].oldValue,
          newValue: prev[key].oldValue,
        }
      }

      return newValues
    })
    resetErrors()
    setIsDisabledBtnSave(true)
  }

  const listOfDirectories = useMemo(() => {
    const arr = values.unloadingDirectories?.newValue as unknown as {
      value: string
    }[]

    return Array.isArray(arr) ? arr.map((item) => ({ value: item?.value, label: item?.value })) : []
  }, [values.unloadingDirectories?.newValue])

  useHotkeys('ctrl+shift+s', () => !isDisabledBtnSave && !isLoadingCreateEdit && handleSave())
  useHotkeys('ctrl+shift+x', () => !(isDisabledBtnSave || !!Object.keys(errors).length) && handleReset())

  const stationList = useMemo(() => {
    switch (reportType) {
      case 'AVRCHM_SUMMARY':
        return plantsParticipateAvrchm
      case 'PLAN_GENERATION':
        return plantsPlanDepartment
      default:
        return []
    }
  }, [reportType, plantsParticipateAvrchm, plantsPlanDepartment])

  const calculationPagePlantsList = useMemo(
    () => [
      {
        id: 0,
        name: 'Свод',
      },
      ...stationList,
    ],
    [stationList],
  )

  const handleEmailsError: AutocompleteEmailsProps['onError'] = (error) => {
    setErrors((prev) => {
      const newErrors = { ...prev }
      if (!error) {
        delete newErrors.emails

        return newErrors
      }

      return {
        ...newErrors,
        emails: error,
      }
    })
  }

  const [wasEditMailSubject, setWasEditMailSubject] = useState(false)

  const handleChangeMailSubject = (value: string) => {
    handleChangeValue('mailSubject', value)
    setWasEditMailSubject(true)
  }

  const handleBlurFileNameTemplate = (value: string) => {
    if (type === 'create' && !wasEditMailSubject) {
      setValues((prev) => ({
        ...prev,
        mailSubject: {
          oldValue: '',
          newValue: value,
        },
      }))
    }
  }

  return (
    <Modal
      open
      title={`${type === 'create' ? 'Создание' : 'Настройка'} отчёта`}
      maxWidth='lg'
      onClose={handleClose}
      actions={
        <div className={cls.modalFooter}>
          <div className={cls.modalFooterRight}>
            {type === 'edit' && (
              <Button variant='outlined' onClick={handleReset} disabled={isDisabledBtnSave || isLoadingCreateEdit}>
                Сбросить
              </Button>
            )}
            <LoadingButton
              variant='contained'
              loading={isLoadingCreateEdit}
              onClick={handleSave}
              disabled={isDisabledBtnSave || !!Object.keys(errors).length}
              className={classNames(cls.saveButton, { [cls.saveButtonEdit]: type !== 'create' })}
            >
              {type === 'create' ? 'Создать' : 'Сохранить'}
            </LoadingButton>
          </div>
        </div>
      }
    >
      <div className={classNames(cls.wrapper, { [cls.wrapperLoading]: isLoadingValues }, [])}>
        {isLoadingValues ? (
          <Loader />
        ) : (
          <>
            <div className={cls.row}>
              <div className={cls.labelRow}>Название</div>
              <div className={cls.valueBlock}>
                <TextField
                  style={{ width: '100%' }}
                  error={!!errors.name?.length}
                  helperText={errors.name ?? ''}
                  value={(values?.name?.newValue as string) ?? ''}
                  type='string'
                  onChange={(e) => handleChangeValue('name', e.target.value)}
                />
              </div>
            </div>
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Формат выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    variant='outlined'
                    items={unloadingFormatList}
                    value={values.unloadingFormat.newValue as string}
                    onChange={(value) => handleChangeValue('unloadingFormat', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
            )}
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Шаблон выгрузки</div>
                <div className={cls.valueBlockShort}>
                  <Select
                    variant='outlined'
                    items={unloadingTemplateList}
                    value={values.unloadingTemplate.newValue as string}
                    disabled={values.unloadingFormat.newValue !== 'XLSX'}
                    onChange={(value) => handleChangeValue('unloadingTemplate', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
            )}
            <div className={cls.row}>
              <div className={cls.labelRow}>Краткое описание</div>
              <div className={cls.valueBlock}>
                <TextField
                  error={!!errors.description?.length}
                  helperText={errors.description ?? ''}
                  className={cls.textFieldInput}
                  value={values.description.newValue as string}
                  type='string'
                  maxLength={255}
                  onChange={(e) => handleChangeValue('description', e.target.value)}
                />
              </div>
            </div>
            <div className={cls.row}>
              <div className={cls.labelRow}>
                <Switch
                  checked={values.putDate.newValue as boolean}
                  onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                    handleChangeValue('putDate', checked)
                  }
                  label='Выгружать дату'
                />
              </div>
              {values.putDate.newValue && (
                <div className={cls.valueBlock}>
                  <TextField
                    label='Строка'
                    error={!!errors.dateRow?.length}
                    helperText={errors.dateRow ?? ''}
                    value={values.dateRow?.newValue as string}
                    type='number'
                    numberOption={{
                      positive: true,
                      isInteger: true,
                      min: 1,
                    }}
                    inputProps={{ type: 'number' }}
                    className={cls.CellForColumnAndRow}
                    onChange={(e) => handleChangeValue('dateRow', e.target.value)}
                  />
                  <TextField
                    label='Столбец'
                    error={!!errors.dateColumn?.length}
                    helperText={errors.dateColumn ?? ''}
                    value={values.dateColumn?.newValue as string}
                    type='number'
                    numberOption={{
                      positive: true,
                      isInteger: true,
                      min: 1,
                    }}
                    inputProps={{ type: 'number' }}
                    className={cls.CellForColumnAndRow}
                    onChange={(e) => handleChangeValue('dateColumn', e.target.value)}
                  />
                </div>
              )}
            </div>
            <div className={cls.row}>
              <div className={cls.labelRow}>
                <Switch
                  checked={values.summaryFormat.newValue as boolean}
                  onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                    handleChangeValue('summaryFormat', checked)
                  }
                  label='По форме Свод'
                />
              </div>
            </div>
            <div className={cls.row}>
              <div className={cls.labelRow}>
                <Switch
                  checked={values.putHeader.newValue as boolean}
                  onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                    handleChangeValue('putHeader', checked)
                  }
                  disabled={!values.summaryFormat.newValue}
                  label='Выгружать шапку'
                />
              </div>
            </div>
            <div className={cls.row}>
              <div className={cls.labelRow}>Станции</div>
              <div className={cls.valueBlockFullWidth}>
                <Autocomplete
                  multiple
                  options={stationList}
                  getOptionLabel={(option: string | IDepAndAvrchmsItem) => {
                    if (typeof option === 'string') {
                      return option
                    }

                    return option.name || ''
                  }}
                  filterSelectedOptions
                  disabled={values.summaryFormat.newValue as boolean}
                  value={values.plants.newValue as Array<string | IDepAndAvrchmsItem>}
                  onChange={(_: SyntheticEvent, value: Array<string | IDepAndAvrchmsItem>) =>
                    handleChangeValue('plants', value as unknown as string)
                  }
                  renderInput={(params) => (
                    <TextField {...params} error={!!errors.plants?.length} helperText={errors.plants ?? ''} />
                  )}
                  className={cls.autocomplite}
                />
              </div>
            </div>
            {reportType === 'AVRCHM_SUMMARY' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>АВРЧМ ТЭС</div>
                <div className={cls.valueBlockFullWidth}>
                  <Checkbox
                    checked={values.avrcmTes.newValue as boolean}
                    disabled={values.summaryFormat.newValue as boolean}
                    onChange={(e: ChangeEvent<HTMLInputElement>) => handleChangeValue('avrcmTes', e.target.checked)}
                  />
                </div>
              </div>
            )}
            <div className={cls.row}>
              <div className={cls.labelRow}>Шаблон названия файла</div>
              <div className={cls.valueBlock}>
                <TextFieldWithPrompt
                  error={!!errors.fileNameTemplate?.length}
                  helperText={errors.fileNameTemplate ?? ''}
                  className={cls.textFieldPropmt}
                  value={values.fileNameTemplate.newValue as string}
                  items={namePlaceholders}
                  onChange={(value: string) => handleChangeValue('fileNameTemplate', value)}
                  onBlur={handleBlurFileNameTemplate}
                />
              </div>
            </div>
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.tableTitle.newValue as boolean}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangeValue('tableTitle', checked)
                    }
                    disabled={values.unloadingFormat.newValue === 'CSV'}
                    label='Заголовок таблицы'
                  />
                </div>
                {}
                {values.tableTitle.newValue && (
                  <div className={cls.valueBlock}>
                    <TextFieldWithPrompt
                      error={!!errors.tableTitleTemplate?.length}
                      helperText={errors.tableTitleTemplate ?? ''}
                      className={cls.textFieldPropmt}
                      value={values.tableTitleTemplate.newValue as string}
                      items={tableNamePlaceholders}
                      onChange={(value: string) => handleChangeValue('tableTitleTemplate', value)}
                      disabled={values.unloadingFormat.newValue === 'CSV'}
                    />
                    <div className={cls.topMargin}>
                      <TextField
                        value={values['tableTitleRow']?.newValue as string}
                        error={!!errors.tableTitleRow?.length}
                        helperText={errors.tableTitleRow ?? ''}
                        type='number'
                        label='Строка'
                        numberOption={{
                          positive: true,
                          isInteger: true,
                          min: 1,
                        }}
                        inputProps={{ type: 'number' }}
                        disabled={values.unloadingFormat.newValue === 'CSV'}
                        onChange={(e) => handleChangeValue('tableTitleRow', e.target.value)}
                        className={cls.CellForColumnAndRow}
                      />
                      <TextField
                        value={values['tableTitleColumn']?.newValue as string}
                        error={!!errors.tableTitleColumn?.length}
                        helperText={errors.tableTitleColumn ?? ''}
                        type='number'
                        label='Столбец'
                        numberOption={{
                          positive: true,
                          isInteger: true,
                          min: 1,
                        }}
                        inputProps={{ type: 'number' }}
                        disabled={values.unloadingFormat.newValue === 'CSV'}
                        onChange={(e) => handleChangeValue('tableTitleColumn', e.target.value)}
                        className={cls.CellForColumnAndRow}
                      />
                    </div>
                  </div>
                )}
              </div>
            )}
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.tableHeader.newValue as boolean}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangeValue('tableHeader', checked)
                    }
                    disabled={values.unloadingFormat.newValue === 'CSV'}
                    label='Шапка таблицы'
                  />
                </div>
                {}
                {values.tableHeader.newValue && (
                  <div className={cls.topMargin}>
                    <TextField
                      value={values['tableHeaderRow']?.newValue as string}
                      error={!!errors.tableHeaderRow?.length}
                      helperText={errors.tableHeaderRow ?? ''}
                      type='number'
                      label='Строка'
                      numberOption={{
                        positive: true,
                        isInteger: true,
                        min: 1,
                      }}
                      inputProps={{ type: 'number' }}
                      disabled={values.unloadingFormat.newValue === 'CSV'}
                      onChange={(e) => handleChangeValue('tableHeaderRow', e.target.value)}
                      className={cls.CellForColumnAndRow}
                    />
                  </div>
                )}
              </div>
            )}
            {reportType === 'AVRCHM_SUMMARY' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Путь к шаблону</div>
                <div className={cls.valueBlock}>
                  <TextField
                    error={!!errors.templatePath?.length}
                    helperText={errors.templatePath ?? ''}
                    value={values.templatePath?.newValue as string}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangeValue('templatePath', e.target.value)}
                    placeholder='\\192.168.10.10\neptune\file.xls'
                  />
                </div>
              </div>
            )}
            <div className={cls.row}>
              <div className={cls.labelRow}>1-й час</div>
              <div className={cls.valueBlock}>
                <TextField
                  label='Строка'
                  error={!!errors.firstHourRow?.length}
                  helperText={errors.firstHourRow ?? ''}
                  value={values.firstHourRow?.newValue as string}
                  type='number'
                  numberOption={{
                    positive: true,
                    isInteger: true,
                    min: 1,
                  }}
                  inputProps={{ type: 'number' }}
                  className={cls.CellForColumnAndRow}
                  disabled={reportType === 'PLAN_GENERATION' && values.unloadingFormat.newValue === 'CSV'}
                  onChange={(e) => handleChangeValue('firstHourRow', e.target.value)}
                />
                {reportType === 'PLAN_GENERATION' && (
                  <TextField
                    label='Столбец'
                    error={!!errors.firstHourColumn?.length}
                    helperText={errors.firstHourColumn ?? ''}
                    value={values.firstHourColumn?.newValue as string}
                    type='number'
                    numberOption={{
                      positive: true,
                      isInteger: true,
                      min: 1,
                    }}
                    inputProps={{ type: 'number' }}
                    className={cls.CellForColumnAndRow}
                    disabled={reportType === 'PLAN_GENERATION' && values.unloadingFormat.newValue === 'CSV'}
                    onChange={(e) => handleChangeValue('firstHourColumn', e.target.value)}
                  />
                )}
                {reportType === 'AVRCHM_SUMMARY' && values.summaryFormat.newValue && (
                  <TextField
                    label='Столбец'
                    error={!!errors.firstHourColumn?.length}
                    helperText={errors.firstHourColumn ?? ''}
                    value={values.firstHourColumn?.newValue as string}
                    type='number'
                    numberOption={{
                      positive: true,
                      isInteger: true,
                      min: 1,
                    }}
                    inputProps={{ type: 'number' }}
                    className={cls.CellForColumnAndRow}
                    onChange={(e) => handleChangeValue('firstHourColumn', e.target.value)}
                  />
                )}
              </div>
            </div>
            {reportType === 'AVRCHM_SUMMARY' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Директория для сохранения</div>
                <div className={cls.valueBlock}>
                  <TextField
                    error={!!errors.unloadingDirectory?.length}
                    helperText={errors.unloadingDirectory ?? ''}
                    value={values.unloadingDirectory?.newValue as string}
                    type='string'
                    className={cls.textFieldInput}
                    onChange={(e) => handleChangeValue('unloadingDirectory', e.target.value)}
                    placeholder='\\192.168.10.10\neptune'
                  />
                </div>
              </div>
            )}
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Директории выгрузки</div>
                <div className={cls.valueBlock}>
                  <GroupsInput
                    error={!!errors.unloadingDirectories?.length}
                    helperText={errors.unloadingDirectories ?? ''}
                    values={values.unloadingDirectories.newValue as unknown as ValueGroups[]}
                    maxItems={5}
                    type='directory'
                    onChange={(arr) => handleChangeValue('unloadingDirectories', arr as unknown as string)}
                  />
                </div>
              </div>
            )}
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    checked={values.unloadingNextDaysTurnedOn.newValue as boolean}
                    onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                      handleChangeValue('unloadingNextDaysTurnedOn', checked)
                    }
                    label='Выгрузка по дням'
                  />
                </div>
                <div className={cls.valueBlockFullWidth}>
                  <div className={cls.valueBlockDays}>
                    {}
                    {values.unloadingNextDaysTurnedOn.newValue &&
                      LIST_DAYS_WEEK.map((item) => (
                        <div key={`unloadingNextDays-${item.value}`} className={cls.column}>
                          <div className={cls.dayWeekLabel}>{item.label}</div>
                          <TextField
                            value={values[`unloadingNextDays${item.value}`]?.newValue as string}
                            error={!!errors[`unloadingNextDays${item.value}`]?.length}
                            type='number'
                            numberOption={{
                              positive: true,
                              isInteger: true,
                              max: 14,
                            }}
                            onChange={(e) => handleChangeValue(`unloadingNextDays${item.value}`, e.target.value)}
                          />
                        </div>
                      ))}
                  </div>
                  {!!errors.unloadingNextDaysTurnedOn?.length && (
                    <div className={cls.errorLabel}>{errors.unloadingNextDaysTurnedOn ?? ''}</div>
                  )}
                </div>
              </div>
            )}
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>
                  <Switch
                    disabled={!values.unloadingNextDaysTurnedOn.newValue}
                    checked={values.offsetDaysTurnedOn.newValue as boolean}
                    onChange={(_: unknown, checked: boolean) => handleChangeValue('offsetDaysTurnedOn', checked)}
                    label='Смещение 1 дня'
                  />
                </div>
                <div className={cls.valueBlockFullWidth}>
                  <div className={cls.valueBlockDays}>
                    {}
                    {values.offsetDaysTurnedOn.newValue &&
                      LIST_DAYS_WEEK.map((item) => (
                        <div key={`offsetDays-${item.value}`} className={cls.column}>
                          <div className={cls.dayWeekLabel}>{item.label}</div>
                          <TextField
                            value={values[`offsetDays${item.value}`]?.newValue as string}
                            error={!!errors[`offsetDays${item.value}`]?.length}
                            type='number'
                            numberOption={{
                              min: -7,
                              max: 5,
                            }}
                            onChange={(e) => handleChangeValue(`offsetDays${item.value}`, e.target.value)}
                          />
                        </div>
                      ))}
                  </div>
                  {!!errors.offsetDaysTurnedOn?.length && (
                    <div className={cls.errorLabel}>{errors.offsetDaysTurnedOn ?? ''}</div>
                  )}
                </div>
              </div>
            )}
            <div className={cls.row}>
              <div className={cls.labelRow}>Тема письма</div>
              <div className={cls.valueBlock}>
                <TextFieldWithPrompt
                  error={!!errors.mailSubject?.length}
                  helperText={errors.mailSubject ?? ''}
                  className={cls.textFieldPropmt}
                  value={values.mailSubject.newValue as string}
                  items={namePlaceholders}
                  onChange={handleChangeMailSubject}
                />
              </div>
            </div>
            {reportType === 'PLAN_GENERATION' && (
              <div className={cls.row}>
                <div className={cls.labelRow}>Директория для рассылки</div>
                <div className={cls.valueBlock}>
                  <Select
                    disabled={!listOfDirectories.length}
                    variant='outlined'
                    items={listOfDirectories}
                    value={values.mailingReportDirectory.newValue as string}
                    onChange={(value) => handleChangeValue('mailingReportDirectory', value)}
                    className={cls.selectFontSize}
                  />
                </div>
              </div>
            )}
            <div className={cls.row}>
              <div className={cls.labelRow}>Получатели рассылки</div>
              <div className={cls.valueBlockFullWidth}>
                <AutocompleteEmails
                  error={!!errors.emails?.length}
                  value={values.emails.newValue as string[]}
                  onValuesChange={(values) => handleChangeValue('emails', values)}
                  onError={handleEmailsError}
                  className={cls.autocomplite}
                />
              </div>
            </div>
            <div className={cls.row}>
              <div className={cls.labelRow}>Вложение по умолчанию</div>
              <div className={cls.valueBlock}>
                <Select
                  variant='outlined'
                  items={typesMailingAttachment}
                  value={values.defaultMailingAttachment.newValue}
                  onChange={(value) => handleChangeValue('defaultMailingAttachment', value)}
                  className={cls.selectFontSize}
                />
              </div>
            </div>

            <div className={cls.row}>
              <div className={cls.labelRow}>От кого</div>
              <div className={cls.valueBlock}>
                <TextField
                  multiline
                  rows={3}
                  maxLength={255}
                  value={values.messageBody?.newValue as string}
                  type='string'
                  className={cls.textFieldInput}
                  onChange={(e) => handleChangeValue('messageBody', e.target.value)}
                />
              </div>
            </div>

            <div className={cls.row}>
              <div className={cls.labelRow}>
                <Switch
                  checked={values.sendFromCalculationPage.newValue as boolean}
                  onChange={(_: ChangeEvent<HTMLInputElement>, checked: boolean) =>
                    handleChangeValue('sendFromCalculationPage', checked)
                  }
                  label='Рассылать с формы Расчетов'
                />
              </div>
              {!!values?.sendFromCalculationPage.newValue && (
                <div className={cls.valueBlockFullWidth}>
                  <Autocomplete
                    multiple
                    options={calculationPagePlantsList}
                    getOptionLabel={(option) => {
                      if (typeof option === 'string') {
                        return option
                      }

                      return option.name || ''
                    }}
                    filterSelectedOptions
                    value={values.calculationPagePlants.newValue as Array<string | IDepAndAvrchmsItem>}
                    onChange={(_: SyntheticEvent, value: Array<string | IDepAndAvrchmsItem>) =>
                      handleChangeValue('calculationPagePlants', value as unknown as string)
                    }
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        error={!!errors.calculationPagePlants?.length}
                        helperText={errors.calculationPagePlants ?? ''}
                      />
                    )}
                    className={cls.autocomplite}
                  />
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </Modal>
  )
})
