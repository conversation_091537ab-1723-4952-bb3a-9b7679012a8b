# neptune-web

## 📋 Содержание

- [Системные требования](#-системные-требования)
- [Настройка окружения разработки](#️-настройка-окружения-разработки)
- [Установка зависимостей](#-установка-зависимостей)
- [Запуск проекта](#-запуск-проекта)
- [Тестирование](#-тестирование)
- [Настройка ESLint](#-настройка-eslint)
- [Доступные команды](#-доступные-команды)

## 🛠 Системные требования

- **Node.js**: v18.20.4 (рекомендуется использовать FNM для автоматического управления версиями)

## ⚙️ Настройка окружения разработки

### Автоматическая смена версии Node.js с помощью FNM

В данном руководстве описаны шаги для автоматической смены версии Node.js при переходе в папку проекта с использованием утилиты [fnm (Fast Node Manager)](https://github.com/Schniz/fnm).

#### Установка FNM

Если у вас еще не установлен FNM, выполните следующие шаги для его установки:

- **Используя скрипт (macOS/Linux)**

  ```bash
  curl -fsSL https://fnm.vercel.app/install | bash
  ```

- **Используя Homebrew (macOS/Linux)**

  ```bash
  brew install fnm
  ```

- **Используя Winget (Windows)**

  ```bash
  winget install Schniz.fnm
  ```

- **Используя Scoop (Windows)**

  ```bash
  scoop install fnm
  ```

- **Используя Chocolatey (Windows)**

  ```bash
  choco install fnm
  ```

#### Настройка автоматического переключения версии Node.js

##### 1. Файл `.nvmrc`

В корне проекта находится файл `.nvmrc`, в котором будет указана версия Node.js, рекомендуемая для использования на данном проекте. Например:

```bash
❯ cat ./neptune-web/.nvmrc
v18.20.4
```

##### 2. Настройка автоматического переключения версии Node.js с помощью оболочки

FNM автоматически переключает версию Node.js при переходе в директорию с проектом, в котором есть файл `.nvmrc`. Для этого необходимо добавить команду инициализации FNM в файл конфигурации вашей оболочки.

- **Для `bash`**

Добавьте следующую строку в файл `~/.bashrc`:

```bash
eval "$(fnm env --use-on-cd --shell bash)"
```

- **Для `zsh`**

Добавьте следующую строку в файл `~/.zshrc`:

```bash
eval "$(fnm env --use-on-cd --shell zsh)"
```

- **Для `fish`**

Создайте файл `~/.config/fish/config.fish` и добавьте следующую строку:

```bash
fnm env --use-on-cd --shell fish | source
```

- **Для `PowerShell`**

Добавьте следующую строку в конец файла с вашим профилем:

```bash
fnm env --use-on-cd --shell power-shell | Out-String | Invoke-Expression
```

- Для macOS/Linux, файл с профилем расположен
  
  ```bash
  ~/.config/powershell/Microsoft.PowerShell_profile.ps1
  ```

- На Windows отредактировать файл с профилем можно через PowerShell командой

  ```bash
  notepad $profile
  ```

##### 3. Проверка работоспособности

При переходе в папку проекта с файлом `.nvmrc`, FNM автоматически будет переключать версию Node.js. Пример:

```bash
❯ cd ../front
Using Node v12.22.12
❯ cd ../neptune-web
Using Node v18.20.4
```

Если у вас не установлена версия Node.js, которая указана в файле `.nvmrc`, FNM предложит ее установить. Пример:

```bash
cd ./neptune-web
Can't find an installed Node version matching v18.20.4.
Do you want to install it? answer [y/N]: y
```

Чтобы убедиться, что всё работает, выполните следующую команду в терминале после перехода в папку проекта:

```bash
node -v
```

Версия Node.js должна соответствовать той, которая указана в файле `.nvmrc`

#### Дополнительно

Рекомендуется добавить флаг `--version-file-strategy=recursive`. Это позволяет FNM рекурсивно искать файл с версией Node.js `.nvmrc` в родительских директориях. Это полезно в том случае, когда вы находитесь в подпапке проекта, но хотите, чтобы FNM автоматически нашёл файл с версией Node.js, указанный в более высоком уровне иерархии директорий. Например:

```bash
repo/
├── package.json
├── .nvmrc <- с версией Node.js: `18.20.4`
└── src/
  └── app/ <- Я здесь
    └── App.tsx
```

По аналогии выше, необходимо добавить строчку в конфигурационный файл вашей оболочки (`~/.bashrc,` `~/.zshrc` и др.):

```bash
eval "$(fnm env --use-on-cd --version-file-strategy=recursive)"
```

## 📦 Установка зависимостей

Выполните следующую команду чтобы установить все зависимости:

```bash
npm i
```

## 🚀 Запуск проекта

- **Запуск в режиме разработки:**

```bash
npm run start
```

- **Запуск с доступом по сети:**

```bash
npm run start:host
```

## 🧪 Тестирование

- **Для запуска тестирования команда:**

```bash
npm run unit
```

- **Просмотр покрытия тестами:**

```bash
npm run unit:coverage
```

## 🔧 Настройка ESLint

### Установить зависимость

```bash
npm install --save-dev @ic-ntcees/eslint-config-react
```

### Выполнить шаги описанные в библиотеке

[Ссылка](https://www.npmjs.com/package/@ic-ntcees/eslint-config-react)

### В package.json добавить команды

```json
{
    "scripts": {
        "lint": "eslint",
        "lint:fix": "eslint --fix"
    }
}
```

#### Для VS Code

- Установить расширения [esbenp.prettier-vscode](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode) & [dbaeumer.vscode-eslint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint)
- Добавить в файл настроек (User settings JSON -> Open with: "Ctrl + Shift + P" -> Start to type: "User Settings"):

```json
{
    "eslint.enable": true,
    "eslint.format.enable": true,
    "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"],
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "eslint.useFlatConfig": true,
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "always",
        "source.addMissingImports": "always"
    }
}
```

#### Для WebStorm

[Ссылка на настройку](https://blog.jetbrains.com/webstorm/2016/08/using-external-tools/)

## 📜 Доступные команды

| Команда | Описание |
|---------|----------|
| `npm run start` | Запуск проекта в режиме разработки |
| `npm run start:host` | Запуск с доступом по сети |
| `npm run build` | Сборка проекта для продакшена |
| `npm run preview:build` | Сборка и предварительный просмотр |
| `npm run preview` | Предварительный просмотр собранного проекта |
| `npm run lint:ts` | Проверка TypeScript файлов линтером |
| `npm run lint:ts:fix` | Автоматическое исправление ошибок линтера |
| `npm run lint` | Проверка и исправление всех файлов |
| `npm run ts:check` | Проверка типов TypeScript |
| `npm run unit` | Запуск юнит-тестов |
| `npm run unit:coverage` | Запуск тестов с отчетом о покрытии |
| `npm run test` | Запуск тестов в watch режиме |
| `npm run lint:prettier` | Форматирование кода с помощью Prettier |
| `npm run commit` | Интерактивный коммит |
| `npm run prepush` | Предпушевая проверка |

## 📝 Дополнительная информация

Осталось отрефакторить:

1. [Table.tsx](src/widgets/Table/ui/Table.tsx)
2. [TableCell.tsx](src/widgets/Table/ui/TableCell.tsx)
